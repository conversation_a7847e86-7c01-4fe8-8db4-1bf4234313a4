# InquirySpring Backend 环境变量配置示例
# 复制此文件为 .env 并填入实际值

# Django配置
DEBUG=True
SECRET_KEY=your-secret-key-here-change-in-production

# AI服务配置
GOOGLE_API_KEY=your_gemini_api_key_here

# 数据库配置（可选，默认使用SQLite）
# DATABASE_URL=postgresql://user:password@localhost:5432/inquiryspring
# DATABASE_URL=mysql://user:password@localhost:3306/inquiryspring

# 文件上传配置
MAX_FILE_SIZE=16777216  # 16MB in bytes

# CORS配置（开发环境）
CORS_ALLOW_ALL_ORIGINS=True
CORS_ALLOWED_ORIGINS=http://localhost:5000,http://127.0.0.1:5000

# 日志级别
LOG_LEVEL=INFO

# 缓存配置（可选）
# REDIS_URL=redis://localhost:6379/0
