<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI服务调试信息查看器</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .log-item {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
        }
        .success {
            border-left-color: #28a745;
        }
        .code {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .loading {
            text-align: center;
            color: #007bff;
        }
        .error-msg {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
        }
        .info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 10px 0;
        }
        .info-item {
            background: #e9ecef;
            padding: 8px;
            border-radius: 3px;
        }
        .info-label {
            font-weight: bold;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 AI服务调试信息查看器</h1>
        
        <div class="section">
            <h2>📋 操作面板</h2>
            <button onclick="loadLatestDebugInfo()">🔄 获取最新调试信息</button>
            <button onclick="loadDebugLogs()">📄 获取调试日志列表</button>
            <button onclick="clearDisplay()">🗑️ 清空显示</button>
        </div>
        
        <div class="section">
            <h2>📊 最新调试信息</h2>
            <div id="latestInfo">点击"获取最新调试信息"按钮查看</div>
        </div>
        
        <div class="section">
            <h2>📄 调试日志列表</h2>
            <div id="logsList">点击"获取调试日志列表"按钮查看</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        
        async function loadLatestDebugInfo() {
            const container = document.getElementById('latestInfo');
            container.innerHTML = '<div class="loading">🔄 加载中...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/test/debug/latest/`);
                const data = await response.json();
                
                if (response.ok) {
                    displayLatestInfo(data);
                } else {
                    container.innerHTML = `<div class="error-msg">❌ ${data.error}</div>`;
                }
            } catch (error) {
                container.innerHTML = `<div class="error-msg">❌ 网络错误: ${error.message}</div>`;
            }
        }
        
        async function loadDebugLogs() {
            const container = document.getElementById('logsList');
            container.innerHTML = '<div class="loading">🔄 加载中...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/test/debug/logs/`);
                const data = await response.json();
                
                if (response.ok) {
                    displayLogsList(data);
                } else {
                    container.innerHTML = `<div class="error-msg">❌ ${data.error}</div>`;
                }
            } catch (error) {
                container.innerHTML = `<div class="error-msg">❌ 网络错误: ${error.message}</div>`;
            }
        }
        
        function displayLatestInfo(data) {
            const container = document.getElementById('latestInfo');
            const debugInfo = data.debug_info;
            const analysis = data.analysis;
            
            let html = `
                <div class="log-item ${analysis.has_error ? 'error' : 'success'}">
                    <h3>📄 ${data.filename}</h3>
                    
                    <div class="info">
                        <div class="info-item">
                            <div class="info-label">请求类型:</div>
                            ${debugInfo.request_type || '未知'}
                        </div>
                        <div class="info-item">
                            <div class="info-label">文档标题:</div>
                            ${debugInfo.document_title || '无'}
                        </div>
                        <div class="info-item">
                            <div class="info-label">题目数量:</div>
                            ${debugInfo.question_count || '未知'}
                        </div>
                        <div class="info-item">
                            <div class="info-label">难度:</div>
                            ${debugInfo.difficulty || '未知'}
                        </div>
                        <div class="info-item">
                            <div class="info-label">是否有错误:</div>
                            ${analysis.has_error ? '❌ 是' : '✅ 否'}
                        </div>
                        <div class="info-item">
                            <div class="info-label">测验数据数量:</div>
                            ${analysis.quiz_data_count}
                        </div>
                    </div>
                    
                    <div style="margin-top: 15px;">
                        <strong>用户查询:</strong>
                        <div class="code">${debugInfo.user_query || '无'}</div>
                    </div>
            `;
            
            if (analysis.has_error) {
                html += `
                    <div style="margin-top: 15px;">
                        <strong>❌ 错误信息:</strong>
                        <div class="code">${analysis.error_message}</div>
                    </div>
                `;
            }
            
            if (analysis.has_text) {
                html += `
                    <div style="margin-top: 15px;">
                        <strong>📝 AI响应文本预览:</strong>
                        <div class="code">${analysis.text_preview}</div>
                    </div>
                `;
            }
            
            html += `
                    <div style="margin-top: 15px;">
                        <strong>🔧 完整原始响应:</strong>
                        <div class="code">${JSON.stringify(debugInfo.raw_ai_response, null, 2)}</div>
                    </div>
                </div>
            `;
            
            container.innerHTML = html;
        }
        
        function displayLogsList(data) {
            const container = document.getElementById('logsList');
            
            if (!data.logs || data.logs.length === 0) {
                container.innerHTML = '<div class="error-msg">📄 没有找到调试日志</div>';
                return;
            }
            
            let html = `<p>📊 共找到 ${data.total_count} 个日志文件，显示最新的 ${data.logs.length} 个：</p>`;
            
            data.logs.forEach((log, index) => {
                html += `
                    <div class="log-item ${log.has_error ? 'error' : 'success'}">
                        <div class="info">
                            <div class="info-item">
                                <div class="info-label">文件名:</div>
                                ${log.filename}
                            </div>
                            <div class="info-item">
                                <div class="info-label">请求类型:</div>
                                ${log.request_type}
                            </div>
                            <div class="info-item">
                                <div class="info-label">文档标题:</div>
                                ${log.document_title}
                            </div>
                            <div class="info-item">
                                <div class="info-label">时间戳:</div>
                                ${log.timestamp}
                            </div>
                            <div class="info-item">
                                <div class="info-label">是否有错误:</div>
                                ${log.has_error ? '❌ 是' : '✅ 否'}
                            </div>
                            <div class="info-item">
                                <div class="info-label">文件大小:</div>
                                ${(log.file_size / 1024).toFixed(1)} KB
                            </div>
                        </div>
                        ${log.error ? `<div class="error-msg">错误: ${log.error}</div>` : ''}
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        function clearDisplay() {
            document.getElementById('latestInfo').innerHTML = '点击"获取最新调试信息"按钮查看';
            document.getElementById('logsList').innerHTML = '点击"获取调试日志列表"按钮查看';
        }
        
        // 页面加载时自动获取最新信息
        window.onload = function() {
            loadLatestDebugInfo();
        };
    </script>
</body>
</html>
