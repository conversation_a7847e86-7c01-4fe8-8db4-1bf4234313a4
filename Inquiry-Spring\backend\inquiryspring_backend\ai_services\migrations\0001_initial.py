# Generated by Django 5.2.1 on 2025-06-02 11:18

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AIModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='模型名称')),
                ('provider', models.CharField(choices=[('gemini', 'Google Gemini'), ('local', '本地模型')], max_length=20, verbose_name='供应商')),
                ('model_id', models.CharField(max_length=100, verbose_name='模型ID')),
                ('api_key', models.CharField(blank=True, max_length=200, verbose_name='API密钥')),
                ('api_base', models.URLField(blank=True, verbose_name='API基础URL')),
                ('max_tokens', models.PositiveIntegerField(default=1000, verbose_name='最大令牌数')),
                ('temperature', models.FloatField(default=0.7, verbose_name='温度参数')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('is_default', models.BooleanField(default=False, verbose_name='是否默认')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': 'AI模型',
                'verbose_name_plural': 'AI模型',
                'ordering': ['-is_default', 'name'],
            },
        ),
        migrations.CreateModel(
            name='PromptTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='模板名称')),
                ('template_type', models.CharField(choices=[('quiz_generation', '测验生成'), ('quiz_without_doc', '无文档测验生成'), ('chat_response', '聊天回复'), ('explanation', '解释生成'), ('summary', '总结生成')], max_length=20, verbose_name='模板类型')),
                ('content', models.TextField(verbose_name='模板内容')),
                ('variables', models.JSONField(blank=True, default=list, verbose_name='变量列表')),
                ('version', models.CharField(default='1.0', max_length=20, verbose_name='版本')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '提示词模板',
                'verbose_name_plural': '提示词模板',
                'ordering': ['template_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='AITaskLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_type', models.CharField(choices=[('quiz_generation', '测验生成'), ('chat', '聊天对话'), ('summary', '文档总结'), ('explanation', '解释生成')], max_length=20, verbose_name='任务类型')),
                ('input_data', models.JSONField(default=dict, verbose_name='输入数据')),
                ('output_data', models.JSONField(blank=True, default=dict, verbose_name='输出数据')),
                ('status', models.CharField(choices=[('pending', '等待中'), ('processing', '处理中'), ('completed', '已完成'), ('failed', '失败')], default='pending', max_length=20, verbose_name='状态')),
                ('error_message', models.TextField(blank=True, verbose_name='错误信息')),
                ('tokens_used', models.PositiveIntegerField(default=0, verbose_name='使用令牌数')),
                ('processing_time', models.FloatField(default=0.0, verbose_name='处理时间(秒)')),
                ('cost', models.DecimalField(decimal_places=4, default=0.0, max_digits=10, verbose_name='成本')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
                ('model', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='ai_services.aimodel', verbose_name='使用模型')),
            ],
            options={
                'verbose_name': 'AI任务日志',
                'verbose_name_plural': 'AI任务日志',
                'ordering': ['-created_at'],
            },
        ),
    ]
