# Generated by Django 5.2 on 2025-06-14 02:37

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("ai_services", "0001_initial"),
        ("documents", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="aitasklog",
            name="document",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="documents.document",
                verbose_name="关联文档",
            ),
        ),
        migrations.AddField(
            model_name="aitasklog",
            name="session_id",
            field=models.CharField(
                blank=True,
                db_index=True,
                help_text="用于追踪匿名用户的会话",
                max_length=100,
                verbose_name="会话ID",
            ),
        ),
        migrations.AddField(
            model_name="aitasklog",
            name="user",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to=settings.AUTH_USER_MODEL,
                verbose_name="关联用户",
            ),
        ),
        migrations.AlterField(
            model_name="aitasklog",
            name="task_type",
            field=models.CharField(
                choices=[
                    ("quiz", "测验生成"),
                    ("chat", "智能对话"),
                    ("summary", "文档总结"),
                ],
                max_length=20,
                verbose_name="任务类型",
            ),
        ),
        migrations.AlterField(
            model_name="prompttemplate",
            name="template_type",
            field=models.CharField(
                choices=[
                    ("quiz", "测验生成"),
                    ("chat", "智能对话"),
                    ("summary", "文档总结"),
                ],
                max_length=20,
                verbose_name="模板类型",
            ),
        ),
    ]
