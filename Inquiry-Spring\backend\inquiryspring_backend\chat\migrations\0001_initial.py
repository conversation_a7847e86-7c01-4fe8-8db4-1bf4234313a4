# Generated by Django 4.2.23 on 2025-06-11 07:02

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ChatSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.CharField(blank=True, max_length=100, null=True, unique=True, verbose_name='会话ID')),
                ('user_message', models.TextField(verbose_name='用户消息')),
                ('ai_response', models.TextField(verbose_name='AI回复')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='时间戳')),
            ],
            options={
                'verbose_name': '聊天会话',
                'verbose_name_plural': '聊天会话',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='Conversation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(blank=True, max_length=200, verbose_name='对话标题')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否活跃')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '对话会话',
                'verbose_name_plural': '对话会话',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField(verbose_name='消息内容')),
                ('is_user', models.BooleanField(default=True, verbose_name='是否用户消息')),
                ('ai_model', models.CharField(blank=True, max_length=100, verbose_name='AI模型')),
                ('processing_time', models.FloatField(default=0.0, verbose_name='处理时间(秒)')),
                ('tokens_used', models.IntegerField(default=0, verbose_name='使用令牌数')),
                ('feedback', models.IntegerField(blank=True, help_text='1=好评, 0=中性, -1=差评', null=True, verbose_name='用户反馈')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('conversation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='chat.conversation')),
            ],
            options={
                'verbose_name': '聊天消息',
                'verbose_name_plural': '聊天消息',
                'ordering': ['created_at'],
            },
        ),
    ]
