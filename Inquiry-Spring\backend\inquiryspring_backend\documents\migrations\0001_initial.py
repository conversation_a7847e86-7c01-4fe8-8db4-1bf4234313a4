# Generated by Django 4.2.23 on 2025-06-11 07:02

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import inquiryspring_backend.documents.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Document',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='文档标题')),
                ('file', models.FileField(upload_to=inquiryspring_backend.documents.models.upload_to, verbose_name='文件')),
                ('file_type', models.CharField(max_length=50, verbose_name='文件类型')),
                ('file_size', models.BigIntegerField(default=0, verbose_name='文件大小')),
                ('is_processed', models.BooleanField(default=False, verbose_name='是否已处理')),
                ('processing_status', models.CharField(default='pending', max_length=50, verbose_name='处理状态')),
                ('error_message', models.TextField(blank=True, verbose_name='错误信息')),
                ('content', models.TextField(blank=True, verbose_name='文档内容')),
                ('summary', models.TextField(blank=True, verbose_name='文档摘要')),
                ('metadata', models.JSONField(blank=True, default=dict, verbose_name='元数据')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='上传时间')),
                ('processed_at', models.DateTimeField(blank=True, null=True, verbose_name='处理时间')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '文档',
                'verbose_name_plural': '文档',
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='UploadedFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('filename', models.CharField(max_length=255, verbose_name='文件名')),
                ('file_path', models.CharField(max_length=500, verbose_name='文件路径')),
                ('file_size', models.BigIntegerField(default=0, verbose_name='文件大小')),
                ('upload_time', models.DateTimeField(auto_now_add=True, verbose_name='上传时间')),
            ],
            options={
                'verbose_name': '上传文件',
                'verbose_name_plural': '上传文件',
                'ordering': ['-upload_time'],
            },
        ),
        migrations.CreateModel(
            name='DocumentChunk',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField(verbose_name='分块内容')),
                ('chunk_index', models.IntegerField(verbose_name='分块索引')),
                ('embedding', models.JSONField(blank=True, null=True, verbose_name='向量嵌入')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='chunks', to='documents.document')),
            ],
            options={
                'verbose_name': '文档分块',
                'verbose_name_plural': '文档分块',
                'ordering': ['document', 'chunk_index'],
            },
        ),
    ]
