# Generated by Django 4.2.23 on 2025-06-11 07:02

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('documents', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='项目名称')),
                ('description', models.TextField(blank=True, verbose_name='项目描述')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否活跃')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '学习项目',
                'verbose_name_plural': '学习项目',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='ProjectStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_documents', models.IntegerField(default=0, verbose_name='文档总数')),
                ('total_chats', models.IntegerField(default=0, verbose_name='聊天总数')),
                ('total_quizzes', models.IntegerField(default=0, verbose_name='测验总数')),
                ('completion_rate', models.FloatField(default=0.0, verbose_name='完成率')),
                ('last_activity', models.DateTimeField(auto_now=True, verbose_name='最后活动时间')),
                ('project', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='stats', to='projects.project')),
            ],
            options={
                'verbose_name': '项目统计',
                'verbose_name_plural': '项目统计',
            },
        ),
        migrations.CreateModel(
            name='ProjectDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('added_at', models.DateTimeField(auto_now_add=True, verbose_name='添加时间')),
                ('is_primary', models.BooleanField(default=False, verbose_name='是否主要文档')),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='documents.document')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='projects.project')),
            ],
            options={
                'verbose_name': '项目文档',
                'verbose_name_plural': '项目文档',
                'unique_together': {('project', 'document')},
            },
        ),
    ]
