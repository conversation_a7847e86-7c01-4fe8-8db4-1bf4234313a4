# Generated by Django 4.2.23 on 2025-06-11 07:02

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Quiz',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='测验标题')),
                ('description', models.TextField(blank=True, verbose_name='测验描述')),
                ('question_count', models.IntegerField(default=5, verbose_name='题目数量')),
                ('difficulty', models.CharField(default='medium', max_length=20, verbose_name='难度')),
                ('question_types', models.J<PERSON>NField(default=list, verbose_name='题目类型')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否活跃')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '测验',
                'verbose_name_plural': '测验',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='QuizAttempt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('score', models.FloatField(default=0.0, verbose_name='得分')),
                ('total_points', models.IntegerField(default=0, verbose_name='总分')),
                ('started_at', models.DateTimeField(auto_now_add=True, verbose_name='开始时间')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
                ('is_completed', models.BooleanField(default=False, verbose_name='是否完成')),
                ('quiz', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attempts', to='quiz.quiz')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '测验尝试',
                'verbose_name_plural': '测验尝试',
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='Question',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question_type', models.CharField(choices=[('MC', '单选题'), ('MCM', '多选题'), ('TF', '判断题'), ('FB', '填空题'), ('SA', '简答题')], max_length=10, verbose_name='题目类型')),
                ('question_text', models.TextField(verbose_name='题目内容')),
                ('options', models.JSONField(blank=True, default=list, verbose_name='选项')),
                ('correct_answer', models.TextField(verbose_name='正确答案')),
                ('explanation', models.TextField(blank=True, verbose_name='解释')),
                ('difficulty', models.CharField(default='medium', max_length=20, verbose_name='难度')),
                ('points', models.IntegerField(default=1, verbose_name='分值')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('quiz', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='quiz.quiz')),
            ],
            options={
                'verbose_name': '题目',
                'verbose_name_plural': '题目',
                'ordering': ['quiz', 'id'],
            },
        ),
        migrations.CreateModel(
            name='Answer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_answer', models.TextField(verbose_name='用户答案')),
                ('is_correct', models.BooleanField(default=False, verbose_name='是否正确')),
                ('points_earned', models.FloatField(default=0.0, verbose_name='获得分数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('attempt', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='answers', to='quiz.quizattempt')),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='quiz.question')),
            ],
            options={
                'verbose_name': '答案',
                'verbose_name_plural': '答案',
                'ordering': ['attempt', 'question'],
            },
        ),
    ]
