# Generated by Django 5.2.1 on 2025-06-16 07:01

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0001_initial'),
        ('quiz', '0002_quiz_source_document'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='quiz',
            name='source_document',
        ),
        migrations.AddField(
            model_name='question',
            name='content',
            field=models.TextField(default='', verbose_name='题目内容'),
        ),
        migrations.AddField(
            model_name='question',
            name='knowledge_points',
            field=models.JSONField(blank=True, default=list, verbose_name='知识点'),
        ),
        migrations.AddField(
            model_name='question',
            name='order',
            field=models.IntegerField(default=0, verbose_name='排序'),
        ),
        migrations.AddField(
            model_name='quiz',
            name='difficulty_level',
            field=models.CharField(default='medium', max_length=20, verbose_name='难度级别'),
        ),
        migrations.AddField(
            model_name='quiz',
            name='document',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='documents.document', verbose_name='关联文档'),
        ),
        migrations.AddField(
            model_name='quiz',
            name='total_questions',
            field=models.IntegerField(default=0, verbose_name='题目总数'),
        ),
        migrations.AlterField(
            model_name='question',
            name='question_text',
            field=models.TextField(blank=True, verbose_name='题目文本'),
        ),
    ]
