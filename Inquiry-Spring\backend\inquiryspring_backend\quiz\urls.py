from django.urls import path
from . import views

# 测验功能URL配置 - 支持文档上传生成测验
urlpatterns = [
    # 前端使用 /api/test/ - 测验生成（支持文档上传）
    path('', views.TestGenerationView.as_view(), name='test_generation'),

    # 专门的文档上传测验生成API
    path('document-quiz/', views.document_quiz_generation, name='document_quiz_generation'),

    # 测验提交
    path('submit/', views.quiz_submit, name='quiz_submit'),

    # 测验历史
    path('history/', views.quiz_history, name='quiz_history'),

    # 测验分析
    path('analysis/<int:attempt_id>/', views.quiz_analysis, name='quiz_analysis'),

    # 可用文档列表
    path('documents/', views.available_documents, name='available_documents'),

    # 调试信息查看
    path('debug/logs/', views.debug_logs, name='debug_logs'),
    path('debug/latest/', views.latest_debug_info, name='latest_debug_info'),
]
