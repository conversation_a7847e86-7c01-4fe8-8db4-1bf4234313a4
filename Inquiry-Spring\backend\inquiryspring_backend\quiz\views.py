import logging
import json
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.conf import settings
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
import os

from .models import Quiz, Question, QuizAttempt, Answer
from ..ai_services.rag_engine import RAGEngine
from ..documents.models import Document

logger = logging.getLogger(__name__)


def save_ai_debug_info(data, filename_prefix="ai_response"):
    """保存AI服务返回的调试信息到JSON文件"""
    try:
        # 创建调试目录
        debug_dir = os.path.join(settings.BASE_DIR, 'debug_logs')
        os.makedirs(debug_dir, exist_ok=True)

        # 生成文件名
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{filename_prefix}_{timestamp}.json"
        filepath = os.path.join(debug_dir, filename)

        # 保存数据
        import json
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        logger.info(f"AI调试信息已保存到: {filepath}")
        return filepath

    except Exception as e:
        logger.error(f"保存AI调试信息失败: {e}")
        return None


@method_decorator(csrf_exempt, name='dispatch')
class TestGenerationView(View):
    """测验生成视图"""

    def get(self, request):
        """获取测验列表或历史"""
        try:
            # 返回最近的测验尝试
            attempts = QuizAttempt.objects.filter(is_completed=True)[:10]
            quiz_list = []

            for attempt in attempts:
                quiz_list.append({
                    'id': attempt.id,
                    'quiz_title': attempt.quiz.title,
                    'score': attempt.score,
                    'total_points': attempt.total_points,
                    'percentage': (attempt.score / attempt.total_points * 100) if attempt.total_points > 0 else 0,
                    'completed_at': attempt.completed_at.isoformat() if attempt.completed_at else None
                })

            return JsonResponse({
                'quizzes': quiz_list,
                'message': '获取测验列表成功'
            })

        except Exception as e:
            logger.error(f"获取测验列表失败: {e}")
            return JsonResponse({'error': str(e)}, status=500)

    def post(self, request):
        """生成测验 - 支持文档上传和基于现有文档生成"""
        try:
            # 检查是否有文件上传
            if request.FILES.get('file'):
                return self._handle_file_upload_and_quiz(request)
            else:
                return self._handle_quiz_generation(request)

        except Exception as e:
            logger.error(f"测验生成失败: {e}")
            return JsonResponse({'error': f'生成失败: {str(e)}'}, status=500)

    def _handle_file_upload_and_quiz(self, request):
        """处理文件上传并生成测验"""
        try:
            # 获取上传的文件
            uploaded_file = request.FILES['file']

            # 获取测验参数
            question_count = int(request.POST.get('num', 5))
            difficulty = request.POST.get('difficulty', 'medium')
            question_types = request.POST.get('types', 'MC,TF').split(',')
            topic = request.POST.get('topic', '')

            logger.info(f"文件上传测验生成: 文件={uploaded_file.name}, 题目数量={question_count}")

            # 创建文档记录
            document = Document.objects.create(
                title=uploaded_file.name,
                file=uploaded_file,
                file_type=self._get_file_type(uploaded_file.name),
                file_size=uploaded_file.size,
                is_processed=False
            )

            # 使用ai_services处理文档
            from inquiryspring_backend.ai_services import process_document_for_rag

            # 处理文档进行RAG
            logger.info(f"开始处理文档进行RAG: {document.id}")

            try:
                rag_success = process_document_for_rag(document.id, force_reprocess=True)
                logger.info(f"RAG处理结果: {rag_success}")
            except Exception as e:
                logger.error(f"RAG处理异常: {e}")
                rag_success = False

            if not rag_success:
                logger.error(f"文档RAG处理失败: {document.id}")
                return JsonResponse({'error': f'文档RAG处理失败，无法生成测验。文档ID: {document.id}'}, status=500)

            # 创建RAG引擎实例并生成测验
            rag_engine = RAGEngine(document_id=document.id)

            user_query = f"基于上传的文档《{document.title}》生成{question_count}道{difficulty}难度的测验题目"
            if topic:
                user_query += f"，重点关注{topic}相关内容"

            # 调用RAG引擎生成测验
            quiz_result = rag_engine.handle_quiz(
                user_query=user_query,
                document_id=document.id,
                question_count=question_count,
                question_types=question_types,
                difficulty=difficulty
            )

            # 保存AI服务返回的原始信息用于调试
            debug_info = {
                'request_type': 'document_upload_quiz',
                'document_title': document.title,
                'document_id': document.id,
                'user_query': user_query,
                'question_count': question_count,
                'difficulty': difficulty,
                'question_types': question_types,
                'raw_ai_response': quiz_result,
                'timestamp': timezone.now().isoformat()
            }
            save_ai_debug_info(debug_info, "document_quiz")

            # 提取测验数据，处理可能的错误
            quiz_data = self._extract_quiz_data_from_rag_result(quiz_result)

            if not quiz_data:
                error_msg = quiz_result.get("error", "测验生成失败，未能提取有效数据")
                logger.error(f"测验数据提取失败: {error_msg}")
                return JsonResponse({'error': error_msg}, status=500)

            # 格式化题目
            formatted_questions = self._format_quiz_questions(quiz_data)

            logger.info(f"基于上传文档生成测验成功: {len(formatted_questions)}道题目")

            return JsonResponse({
                'AIQuestion': formatted_questions,
                'message': f'基于文档《{document.title}》生成测验成功',
                'document_id': document.id,
                'document_title': document.title
            })

        except Exception as e:
            logger.error(f"文件上传测验生成失败: {e}")
            return JsonResponse({'error': f'文件上传生成失败: {str(e)}'}, status=500)

    def _handle_quiz_generation(self, request):
        """处理基于现有文档或主题的测验生成"""
        try:
            data = json.loads(request.body)

            # 获取参数
            question_count = data.get('num', 5)
            difficulty = data.get('difficulty', 'medium')
            question_types = data.get('types', ['MC', 'TF'])
            topic = data.get('topic', '')
            document_id = data.get('document_id')  # 可选：指定文档ID

            logger.info(f"生成测验请求: 题目数量={question_count}, 难度={difficulty}, 类型={question_types}")

            # 创建RAG引擎实例
            rag_engine = RAGEngine()

            # 确定使用的文档
            target_document = None
            if document_id:
                try:
                    target_document = Document.objects.get(id=document_id, is_processed=True)
                except Document.DoesNotExist:
                    logger.warning(f"指定的文档ID {document_id} 不存在或未处理")

            if not target_document:
                # 获取最近上传的文档
                target_document = Document.objects.filter(
                    is_processed=True
                ).order_by('-uploaded_at').first()

            if target_document:
                # 基于文档内容生成测验
                logger.info(f"基于文档生成测验: {target_document.title}")
                user_query = f"基于文档《{target_document.title}》生成{question_count}道{difficulty}难度的测验题目"
                if topic:
                    user_query += f"，重点关注{topic}相关内容"

                quiz_result = rag_engine.handle_quiz(
                    user_query=user_query,
                    document_id=target_document.id,
                    question_count=question_count,
                    question_types=question_types,
                    difficulty=difficulty
                )
            else:
                # 没有文档时使用主题生成
                logger.info("没有可用文档，基于主题生成测验")
                user_query = f"生成关于{topic or '通用知识'}的{question_count}道{difficulty}难度的测验题目"
                quiz_result = rag_engine.handle_quiz(
                    user_query=user_query,
                    question_count=question_count,
                    question_types=question_types,
                    difficulty=difficulty
                )

            # 保存AI服务返回的原始信息用于调试
            debug_info = {
                'request_type': 'normal_quiz',
                'document_title': target_document.title if target_document else None,
                'document_id': target_document.id if target_document else None,
                'user_query': user_query,
                'question_count': question_count,
                'difficulty': difficulty,
                'question_types': question_types,
                'topic': topic,
                'raw_ai_response': quiz_result,
                'timestamp': timezone.now().isoformat()
            }
            save_ai_debug_info(debug_info, "normal_quiz")

            # 提取测验数据，处理可能的错误
            quiz_data = self._extract_quiz_data_from_rag_result(quiz_result)

            if not quiz_data:
                error_msg = quiz_result.get("error", "测验生成失败，未能提取有效数据")
                logger.error(f"测验数据提取失败: {error_msg}")
                return JsonResponse({'error': error_msg}, status=500)

            # 格式化题目
            formatted_questions = self._format_quiz_questions(quiz_data)

            logger.info(f"测验生成成功: {len(formatted_questions)}道题目")

            response_data = {
                'AIQuestion': formatted_questions,
                'message': '测验生成成功'
            }

            if target_document:
                response_data.update({
                    'document_id': target_document.id,
                    'document_title': target_document.title,
                    'based_on_document': True
                })
            else:
                response_data['based_on_document'] = False

            return JsonResponse(response_data)

        except Exception as e:
            logger.error(f"测验生成失败: {e}")
            return JsonResponse({'error': f'生成失败: {str(e)}'}, status=500)

    def _format_quiz_questions(self, quiz_data):
        """格式化测验题目为前端期望的格式"""
        formatted_questions = []
        for i, q in enumerate(quiz_data):
            # 提取题目内容
            question_text = (
                q.get('content') or
                q.get('question_text') or
                q.get('question') or
                q.get('题目') or
                ''
            ).strip()

            # 提取题目类型
            question_type_raw = (
                q.get('type') or
                q.get('question_type') or
                q.get('题型') or
                'MC'
            )

            # 标准化题目类型
            question_type = str(question_type_raw).upper()

            # 映射不标准的题目类型
            type_mapping = {
                'SA': 'MC',  # 简答题转为选择题
                'SHORT_ANSWER': 'MC',
                'ESSAY': 'MC',
                'FILL': 'MC',
                'MULTIPLE_CHOICE': 'MC',
                'TRUE_FALSE': 'TF',
                'BOOLEAN': 'TF'
            }

            if question_type in type_mapping:
                question_type = type_mapping[question_type]
                logger.info(f"题目类型 {question_type_raw} 已映射为 {question_type}")

            # 确保是支持的类型
            if question_type not in ['MC', 'TF']:
                question_type = 'MC'  # 默认为选择题

            # 提取选项
            options_raw = q.get('options', q.get('选项', []))
            if isinstance(options_raw, str):
                # 如果选项是字符串，尝试分割
                options = [opt.strip() for opt in options_raw.split('\n') if opt.strip()]
            elif isinstance(options_raw, list):
                # 如果是列表，确保每个元素都是字符串
                options = [str(opt).strip() for opt in options_raw if opt]
            else:
                options = []

            # 提取正确答案
            correct_answer_raw = (
                q.get('correct_answer') or
                q.get('answer') or
                q.get('正确答案') or
                q.get('答案') or
                ''
            )

            # 处理正确答案的不同格式
            if isinstance(correct_answer_raw, list):
                # 如果是列表，取第一个元素或连接成字符串
                if correct_answer_raw:
                    correct_answer = str(correct_answer_raw[0]).strip()
                else:
                    correct_answer = ''
            else:
                correct_answer = str(correct_answer_raw).strip()

            # 提取解析
            explanation = (
                q.get('explanation') or
                q.get('解析') or
                q.get('解释') or
                ''
            ).strip()

            # 修复常见问题
            if question_type == 'TF' and not correct_answer:
                # 判断题如果没有明确答案，尝试从题目中推断
                if '正确' in question_text or '对' in question_text:
                    correct_answer = '正确'
                elif '错误' in question_text or '错' in question_text:
                    correct_answer = '错误'
                else:
                    correct_answer = '正确'  # 默认

            # 确保选择题有选项
            if question_type == 'MC' and not options:
                # 如果原来是SA类型，尝试从correct_answer中生成选项
                if isinstance(q.get('correct_answer'), list) and len(q.get('correct_answer', [])) > 1:
                    # 使用AI提供的答案作为选项
                    answer_list = q.get('correct_answer', [])
                    options = [f"{chr(65+i)}. {str(ans).strip()}" for i, ans in enumerate(answer_list[:4])]
                    correct_answer = 'A'  # 第一个选项作为正确答案
                else:
                    # 生成通用选项
                    options = ['A. 选项A', 'B. 选项B', 'C. 选项C', 'D. 选项D']
                    if not correct_answer:
                        correct_answer = 'A'

            formatted_q = {
                'id': i + 1,
                'question': question_text,
                'type': question_type,
                'options': options,
                'correct_answer': correct_answer,
                'explanation': explanation,
                'difficulty': q.get('difficulty', q.get('难度', 'medium')),
                'knowledge_points': q.get('knowledge_points', q.get('知识点', []))
            }

            # 只添加有效的题目
            if question_text:
                formatted_questions.append(formatted_q)
            else:
                logger.warning(f"跳过无效题目: {q}")

        return formatted_questions

    def _get_file_type(self, filename):
        """根据文件名确定文件类型"""
        if filename.lower().endswith('.pdf'):
            return 'pdf'
        elif filename.lower().endswith('.docx'):
            return 'docx'
        elif filename.lower().endswith('.txt'):
            return 'txt'
        elif filename.lower().endswith('.md'):
            return 'md'
        else:
            return 'unknown'

    def _extract_quiz_data_from_rag_result(self, quiz_result):
        """从RAG引擎结果中提取测验数据，处理可能的错误"""
        try:
            # 如果有quiz_data，直接返回
            if "quiz_data" in quiz_result and quiz_result["quiz_data"]:
                logger.info(f"直接从quiz_data提取到 {len(quiz_result['quiz_data'])} 道题目")
                return quiz_result["quiz_data"]

            # 如果有text字段，尝试解析
            text_content = quiz_result.get("text", "")
            if text_content:
                logger.info("尝试从text字段解析测验数据")
                parsed_data = self._parse_quiz_from_text(text_content)
                if parsed_data:
                    logger.info(f"从text字段解析到 {len(parsed_data)} 道题目")
                    return parsed_data

            # 如果有error但可能包含有用信息
            if "error" in quiz_result:
                logger.warning(f"RAG引擎返回错误: {quiz_result['error']}")
                # 尝试从错误信息中提取可能的数据
                if "text" in quiz_result:
                    parsed_data = self._parse_quiz_from_text(quiz_result["text"])
                    if parsed_data:
                        logger.info(f"从错误响应中恢复了 {len(parsed_data)} 道题目")
                        return parsed_data

            logger.error("无法从RAG结果中提取有效的测验数据")
            return []

        except Exception as e:
            logger.error(f"提取测验数据失败: {e}")
            return []

    def _parse_quiz_from_text(self, text):
        """从文本中解析测验数据"""
        try:
            import json
            import re

            # 尝试从Markdown代码块中提取JSON
            json_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', text)
            if json_match:
                json_str = json_match.group(1)
                logger.info("找到JSON代码块")
            else:
                json_str = text
                logger.info("使用完整文本作为JSON")

            # 清理JSON字符串
            json_str = self._clean_json_string(json_str)

            try:
                parsed_data = json.loads(json_str)

                # 处理不同的数据结构
                if isinstance(parsed_data, list):
                    return parsed_data
                elif isinstance(parsed_data, dict):
                    # 尝试各种可能的键名
                    for key in ['questions', 'quiz_data', 'data', 'items', '题目']:
                        if key in parsed_data and isinstance(parsed_data[key], list):
                            return parsed_data[key]
                    # 如果是单个题目对象，包装成列表
                    if any(k in parsed_data for k in ['question', 'content', '题目']):
                        return [parsed_data]

            except json.JSONDecodeError as e:
                logger.warning(f"JSON解析失败: {e}")
                # 尝试修复常见的JSON错误
                fixed_json = self._fix_common_json_errors(json_str)
                if fixed_json != json_str:
                    try:
                        parsed_data = json.loads(fixed_json)
                        if isinstance(parsed_data, list):
                            return parsed_data
                        elif isinstance(parsed_data, dict) and 'questions' in parsed_data:
                            return parsed_data['questions']
                    except json.JSONDecodeError:
                        pass

            # 如果JSON解析失败，尝试文本解析
            return self._parse_quiz_from_plain_text(text)

        except Exception as e:
            logger.error(f"解析测验文本失败: {e}")
            return []

    def _clean_json_string(self, json_str):
        """清理JSON字符串"""
        import re

        # 移除注释
        json_str = re.sub(r'//.*?$', '', json_str, flags=re.MULTILINE)
        json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)

        # 移除尾随逗号
        json_str = re.sub(r',(\s*[\]}])', r'\1', json_str)

        return json_str.strip()

    def _fix_common_json_errors(self, json_str):
        """修复常见的JSON错误"""
        import re

        # 修复单引号
        json_str = json_str.replace("'", '"')

        # 修复未引用的键
        json_str = re.sub(r'(\w+):', r'"\1":', json_str)

        # 修复多余的逗号
        json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)

        return json_str

    def _parse_quiz_from_plain_text(self, text):
        """从纯文本中解析测验数据（备用方案）"""
        try:
            # 这是一个简单的文本解析器，用于处理格式化的文本
            questions = []
            lines = text.split('\n')

            current_question = {}
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 检测题目开始
                if line.startswith(('题目', 'Question', '问题')):
                    if current_question:
                        questions.append(current_question)
                    current_question = {'question': line, 'type': 'MC', 'options': [], 'correct_answer': ''}

                # 检测选项
                elif line.startswith(('A.', 'B.', 'C.', 'D.', 'A、', 'B、', 'C、', 'D、')):
                    if 'options' not in current_question:
                        current_question['options'] = []
                    current_question['options'].append(line)

                # 检测答案
                elif line.startswith(('答案', 'Answer', '正确答案')):
                    current_question['correct_answer'] = line.split(':', 1)[-1].strip()

            if current_question:
                questions.append(current_question)

            logger.info(f"从纯文本解析到 {len(questions)} 道题目")
            return questions

        except Exception as e:
            logger.error(f"纯文本解析失败: {e}")
            return []




@api_view(['POST'])
def quiz_submit(request):
    """提交测验答案"""
    try:
        data = request.data
        quiz_id = data.get('quiz_id')
        answers = data.get('answers', [])
        
        if not quiz_id:
            return Response({'error': '缺少测验ID'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            quiz = Quiz.objects.get(id=quiz_id)
        except Quiz.DoesNotExist:
            return Response({'error': '测验不存在'}, status=status.HTTP_404_NOT_FOUND)
        
        # 创建测验尝试
        attempt = QuizAttempt.objects.create(quiz=quiz)
        
        # 处理答案
        total_score = 0
        total_points = 0
        
        for answer_data in answers:
            question_id = answer_data.get('question_id')
            user_answer = answer_data.get('answer', '')
            
            try:
                question = Question.objects.get(id=question_id, quiz=quiz)
                is_correct = user_answer.strip().lower() == question.correct_answer.strip().lower()
                points_earned = question.points if is_correct else 0
                
                Answer.objects.create(
                    attempt=attempt,
                    question=question,
                    user_answer=user_answer,
                    is_correct=is_correct,
                    points_earned=points_earned
                )
                
                total_score += points_earned
                total_points += question.points
                
            except Question.DoesNotExist:
                continue
        
        # 更新尝试结果
        attempt.score = total_score
        attempt.total_points = total_points
        attempt.is_completed = True
        attempt.save()
        
        return Response({
            'message': '测验提交成功',
            'score': total_score,
            'total_points': total_points,
            'percentage': (total_score / total_points * 100) if total_points > 0 else 0
        })
        
    except Exception as e:
        logger.error(f"测验提交失败: {e}")
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def quiz_history(request):
    """获取测验历史"""
    try:
        attempts = QuizAttempt.objects.filter(is_completed=True)[:20]
        history = []
        
        for attempt in attempts:
            history.append({
                'id': attempt.id,
                'quiz_title': attempt.quiz.title,
                'score': attempt.score,
                'total_points': attempt.total_points,
                'percentage': (attempt.score / attempt.total_points * 100) if attempt.total_points > 0 else 0,
                'completed_at': attempt.completed_at.isoformat() if attempt.completed_at else None
            })
        
        return Response({'history': history})
        
    except Exception as e:
        logger.error(f"获取测验历史失败: {e}")
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def quiz_analysis(request, attempt_id):
    """获取测验分析"""
    try:
        attempt = QuizAttempt.objects.get(id=attempt_id)
        answers = Answer.objects.filter(attempt=attempt)

        analysis = {
            'quiz_title': attempt.quiz.title,
            'total_score': attempt.score,
            'total_points': attempt.total_points,
            'percentage': (attempt.score / attempt.total_points * 100) if attempt.total_points > 0 else 0,
            'questions': []
        }

        for answer in answers:
            question_analysis = {
                'question': answer.question.question_text,
                'user_answer': answer.user_answer,
                'correct_answer': answer.question.correct_answer,
                'is_correct': answer.is_correct,
                'explanation': answer.question.explanation,
                'points_earned': answer.points_earned,
                'total_points': answer.question.points
            }
            analysis['questions'].append(question_analysis)

        return Response(analysis)

    except QuizAttempt.DoesNotExist:
        return Response({'error': '测验尝试不存在'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"获取测验分析失败: {e}")
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@csrf_exempt
@api_view(['POST'])
def document_quiz_generation(request):
    """专门用于文档上传生成测验的API端点"""
    try:
        # 检查是否有文件上传
        if 'file' not in request.FILES:
            return Response({'error': '请上传文档文件'}, status=status.HTTP_400_BAD_REQUEST)

        uploaded_file = request.FILES['file']

        # 获取测验参数
        question_count = int(request.data.get('num', 5))
        difficulty = request.data.get('difficulty', 'medium')
        question_types_str = request.data.get('types', 'MC,TF')
        question_types = question_types_str.split(',') if isinstance(question_types_str, str) else question_types_str
        topic = request.data.get('topic', '')

        logger.info(f"文档测验生成API: 文件={uploaded_file.name}, 题目数量={question_count}")

        # 验证文件类型
        allowed_extensions = ['.pdf', '.docx', '.txt', '.md']
        file_extension = '.' + uploaded_file.name.split('.')[-1].lower()
        if file_extension not in allowed_extensions:
            return Response({
                'error': f'不支持的文件类型。支持的格式: {", ".join(allowed_extensions)}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证文件大小 (最大16MB)
        max_size = 16 * 1024 * 1024  # 16MB
        if uploaded_file.size > max_size:
            return Response({
                'error': f'文件大小超过限制。最大支持 {max_size // (1024*1024)}MB'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 创建文档记录
        document = Document.objects.create(
            title=uploaded_file.name,
            file=uploaded_file,
            file_type=file_extension[1:],  # 去掉点号
            file_size=uploaded_file.size,
            is_processed=False
        )

        logger.info(f"文档创建成功: ID={document.id}, 标题={document.title}")

        # 使用ai_services处理文档
        from inquiryspring_backend.ai_services import process_document_for_rag

        # 处理文档进行RAG
        logger.info(f"开始处理文档进行RAG: {document.id}")
        rag_success = process_document_for_rag(document.id, force_reprocess=True)

        if not rag_success:
            logger.error(f"文档RAG处理失败: {document.id}")
            return Response({'error': '文档处理失败，无法生成测验'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        logger.info(f"文档RAG处理成功: {document.id}")

        # 创建RAG引擎实例并生成测验
        rag_engine = RAGEngine(document_id=document.id)

        user_query = f"基于上传的文档《{document.title}》生成{question_count}道{difficulty}难度的测验题目"
        if topic:
            user_query += f"，重点关注{topic}相关内容"

        logger.info(f"开始生成测验: {user_query}")

        quiz_result = rag_engine.handle_quiz(
            user_query=user_query,
            document_id=document.id,
            question_count=question_count,
            question_types=question_types,
            difficulty=difficulty
        )

        if "error" in quiz_result:
            logger.error(f"测验生成失败: {quiz_result['error']}")
            return Response({'error': quiz_result["error"]}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # 格式化题目
        quiz_data = quiz_result.get("quiz_data", [])
        formatted_questions = []

        for i, q in enumerate(quiz_data):
            formatted_q = {
                'id': i + 1,
                'question': q.get('content', q.get('question_text', q.get('question', ''))),
                'type': q.get('type', q.get('question_type', 'MC')),
                'options': q.get('options', []),
                'correct_answer': q.get('correct_answer', ''),
                'explanation': q.get('explanation', ''),
                'difficulty': q.get('difficulty', difficulty),
                'knowledge_points': q.get('knowledge_points', [])
            }
            formatted_questions.append(formatted_q)

        logger.info(f"基于文档生成测验成功: {len(formatted_questions)}道题目")

        return Response({
            'AIQuestion': formatted_questions,
            'message': f'基于文档《{document.title}》生成测验成功',
            'document_id': document.id,
            'document_title': document.title,
            'question_count': len(formatted_questions),
            'file_size': uploaded_file.size,
            'file_type': file_extension[1:]
        })

    except Exception as e:
        logger.error(f"文档测验生成API失败: {e}")
        return Response({'error': f'生成失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def available_documents(request):
    """获取可用于生成测验的文档列表"""
    try:
        documents = Document.objects.filter(is_processed=True).order_by('-uploaded_at')[:20]

        doc_list = []
        for doc in documents:
            doc_list.append({
                'id': doc.id,
                'title': doc.title,
                'file_type': doc.file_type,
                'file_size': doc.file_size,
                'uploaded_at': doc.uploaded_at.isoformat(),
                'processed_at': doc.processed_at.isoformat() if doc.processed_at else None
            })

        return Response({
            'documents': doc_list,
            'count': len(doc_list)
        })

    except Exception as e:
        logger.error(f"获取文档列表失败: {e}")
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def debug_logs(request):
    """获取调试日志列表"""
    try:
        debug_dir = os.path.join(settings.BASE_DIR, 'debug_logs')

        if not os.path.exists(debug_dir):
            return Response({'logs': [], 'message': '调试日志目录不存在'})

        import glob
        log_files = glob.glob(os.path.join(debug_dir, "*.json"))
        log_files.sort(key=os.path.getmtime, reverse=True)

        logs_info = []
        for log_file in log_files[:20]:  # 最多返回20个文件
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                logs_info.append({
                    'filename': os.path.basename(log_file),
                    'request_type': data.get('request_type', '未知'),
                    'document_title': data.get('document_title', '无'),
                    'timestamp': data.get('timestamp', '无'),
                    'has_error': 'error' in data.get('raw_ai_response', {}),
                    'file_size': os.path.getsize(log_file)
                })
            except Exception as e:
                logs_info.append({
                    'filename': os.path.basename(log_file),
                    'error': f'读取失败: {e}'
                })

        return Response({
            'logs': logs_info,
            'total_count': len(log_files)
        })

    except Exception as e:
        return Response({'error': str(e)}, status=500)


@api_view(['GET'])
def latest_debug_info(request):
    """获取最新的调试信息详情"""
    try:
        debug_dir = os.path.join(settings.BASE_DIR, 'debug_logs')

        if not os.path.exists(debug_dir):
            return Response({'error': '调试日志目录不存在'}, status=404)

        import glob
        log_files = glob.glob(os.path.join(debug_dir, "*.json"))

        if not log_files:
            return Response({'error': '没有找到调试日志'}, status=404)

        # 获取最新的文件
        latest_file = max(log_files, key=os.path.getmtime)

        with open(latest_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 分析AI响应
        raw_response = data.get('raw_ai_response', {})
        analysis = {
            'has_error': 'error' in raw_response,
            'has_text': 'text' in raw_response,
            'has_quiz_data': 'quiz_data' in raw_response,
            'text_length': len(raw_response.get('text', '')),
            'quiz_data_count': len(raw_response.get('quiz_data', [])) if isinstance(raw_response.get('quiz_data'), list) else 0
        }

        if analysis['has_error']:
            analysis['error_message'] = raw_response['error']

        if analysis['has_text']:
            analysis['text_preview'] = raw_response['text'][:500] + "..." if len(raw_response['text']) > 500 else raw_response['text']

        return Response({
            'filename': os.path.basename(latest_file),
            'debug_info': data,
            'analysis': analysis,
            'file_time': os.path.getmtime(latest_file)
        })

    except Exception as e:
        return Response({'error': str(e)}, status=500)
