import logging
import json
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View
from django.shortcuts import get_object_or_404
from django.utils import timezone
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status

from .models import Quiz, Question, QuizAttempt, Answer
from ..ai_services.rag_engine import RAGEngine
from ..documents.models import Document

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
class TestGenerationView(View):
    """测验生成视图"""

    def get(self, request):
        """获取测验列表或历史"""
        try:
            # 返回最近的测验尝试
            attempts = QuizAttempt.objects.filter(is_completed=True)[:10]
            quiz_list = []

            for attempt in attempts:
                quiz_list.append({
                    'id': attempt.id,
                    'quiz_title': attempt.quiz.title,
                    'score': attempt.score,
                    'total_points': attempt.total_points,
                    'percentage': (attempt.score / attempt.total_points * 100) if attempt.total_points > 0 else 0,
                    'completed_at': attempt.completed_at.isoformat() if attempt.completed_at else None
                })

            return JsonResponse({
                'quizzes': quiz_list,
                'message': '获取测验列表成功'
            })

        except Exception as e:
            logger.error(f"获取测验列表失败: {e}")
            return JsonResponse({'error': str(e)}, status=500)

    def post(self, request):
        """生成测验 - 支持文档上传和基于现有文档生成"""
        try:
            # 检查是否有文件上传
            if request.FILES.get('file'):
                return self._handle_file_upload_and_quiz(request)
            else:
                return self._handle_quiz_generation(request)

        except Exception as e:
            logger.error(f"测验生成失败: {e}")
            return JsonResponse({'error': f'生成失败: {str(e)}'}, status=500)

    def _handle_file_upload_and_quiz(self, request):
        """处理文件上传并生成测验"""
        try:
            # 获取上传的文件
            uploaded_file = request.FILES['file']

            # 获取测验参数
            question_count = int(request.POST.get('num', 5))
            difficulty = request.POST.get('difficulty', 'medium')
            question_types = request.POST.get('types', 'MC,TF').split(',')
            topic = request.POST.get('topic', '')

            logger.info(f"文件上传测验生成: 文件={uploaded_file.name}, 题目数量={question_count}")

            # 创建文档记录
            document = Document.objects.create(
                title=uploaded_file.name,
                file=uploaded_file,
                file_type=self._get_file_type(uploaded_file.name),
                file_size=uploaded_file.size,
                is_processed=False
            )

            # 使用ai_services处理文档
            from inquiryspring_backend.ai_services import process_document_for_rag

            # 处理文档进行RAG
            rag_success = process_document_for_rag(document.id, force_reprocess=True)

            if not rag_success:
                return JsonResponse({'error': '文档处理失败，无法生成测验'}, status=500)

            # 创建RAG引擎实例并生成测验
            rag_engine = RAGEngine(document_id=document.id)

            user_query = f"基于上传的文档《{document.title}》生成{question_count}道{difficulty}难度的测验题目"
            if topic:
                user_query += f"，重点关注{topic}相关内容"

            # 调用RAG引擎生成测验，但不保存到数据库（因为字段不匹配）
            quiz_result = rag_engine.handle_quiz(
                user_query=user_query,
                document_id=document.id,
                question_count=question_count,
                question_types=question_types,
                difficulty=difficulty
            )

            # 如果RAG引擎尝试保存失败，我们手动处理quiz_data
            if "error" in quiz_result and "quiz_data" in quiz_result:
                # 从错误中恢复，使用quiz_data
                pass
            elif "quiz_data" not in quiz_result:
                # 如果没有quiz_data，说明生成失败
                return JsonResponse({'error': quiz_result.get("error", "测验生成失败")}, status=500)

            # 提取测验数据，处理可能的错误
            quiz_data = self._extract_quiz_data_from_rag_result(quiz_result)

            if not quiz_data:
                error_msg = quiz_result.get("error", "测验生成失败，未能提取有效数据")
                return JsonResponse({'error': error_msg}, status=500)

            # 格式化题目
            formatted_questions = self._format_quiz_questions(quiz_data)

            logger.info(f"基于上传文档生成测验成功: {len(formatted_questions)}道题目")

            return JsonResponse({
                'AIQuestion': formatted_questions,
                'message': f'基于文档《{document.title}》生成测验成功',
                'document_id': document.id,
                'document_title': document.title
            })

        except Exception as e:
            logger.error(f"文件上传测验生成失败: {e}")
            return JsonResponse({'error': f'文件上传生成失败: {str(e)}'}, status=500)

    def _handle_quiz_generation(self, request):
        """处理基于现有文档或主题的测验生成"""
        try:
            data = json.loads(request.body)

            # 获取参数
            question_count = data.get('num', 5)
            difficulty = data.get('difficulty', 'medium')
            question_types = data.get('types', ['MC', 'TF'])
            topic = data.get('topic', '')
            document_id = data.get('document_id')  # 可选：指定文档ID

            logger.info(f"生成测验请求: 题目数量={question_count}, 难度={difficulty}, 类型={question_types}")

            # 创建RAG引擎实例
            rag_engine = RAGEngine()

            # 确定使用的文档
            target_document = None
            if document_id:
                try:
                    target_document = Document.objects.get(id=document_id, is_processed=True)
                except Document.DoesNotExist:
                    logger.warning(f"指定的文档ID {document_id} 不存在或未处理")

            if not target_document:
                # 获取最近上传的文档
                target_document = Document.objects.filter(
                    is_processed=True
                ).order_by('-uploaded_at').first()

            if target_document:
                # 基于文档内容生成测验
                logger.info(f"基于文档生成测验: {target_document.title}")
                user_query = f"基于文档《{target_document.title}》生成{question_count}道{difficulty}难度的测验题目"
                if topic:
                    user_query += f"，重点关注{topic}相关内容"

                quiz_result = rag_engine.handle_quiz(
                    user_query=user_query,
                    document_id=target_document.id,
                    question_count=question_count,
                    question_types=question_types,
                    difficulty=difficulty
                )
            else:
                # 没有文档时使用主题生成
                logger.info("没有可用文档，基于主题生成测验")
                user_query = f"生成关于{topic or '通用知识'}的{question_count}道{difficulty}难度的测验题目"
                quiz_result = rag_engine.handle_quiz(
                    user_query=user_query,
                    question_count=question_count,
                    question_types=question_types,
                    difficulty=difficulty
                )

            # 提取测验数据，处理可能的错误
            quiz_data = self._extract_quiz_data_from_rag_result(quiz_result)

            if not quiz_data:
                error_msg = quiz_result.get("error", "测验生成失败，未能提取有效数据")
                return JsonResponse({'error': error_msg}, status=500)

            # 格式化题目
            formatted_questions = self._format_quiz_questions(quiz_data)

            logger.info(f"测验生成成功: {len(formatted_questions)}道题目")

            response_data = {
                'AIQuestion': formatted_questions,
                'message': '测验生成成功'
            }

            if target_document:
                response_data.update({
                    'document_id': target_document.id,
                    'document_title': target_document.title,
                    'based_on_document': True
                })
            else:
                response_data['based_on_document'] = False

            return JsonResponse(response_data)

        except Exception as e:
            logger.error(f"测验生成失败: {e}")
            return JsonResponse({'error': f'生成失败: {str(e)}'}, status=500)

    def _format_quiz_questions(self, quiz_data):
        """格式化测验题目为前端期望的格式"""
        formatted_questions = []
        for i, q in enumerate(quiz_data):
            formatted_q = {
                'id': i + 1,
                'question': q.get('content', q.get('question_text', q.get('question', ''))),
                'type': q.get('type', q.get('question_type', 'MC')),
                'options': q.get('options', []),
                'correct_answer': q.get('correct_answer', ''),
                'explanation': q.get('explanation', ''),
                'difficulty': q.get('difficulty', 'medium'),
                'knowledge_points': q.get('knowledge_points', [])
            }
            formatted_questions.append(formatted_q)
        return formatted_questions

    def _get_file_type(self, filename):
        """根据文件名确定文件类型"""
        if filename.lower().endswith('.pdf'):
            return 'pdf'
        elif filename.lower().endswith('.docx'):
            return 'docx'
        elif filename.lower().endswith('.txt'):
            return 'txt'
        elif filename.lower().endswith('.md'):
            return 'md'
        else:
            return 'unknown'

    def _extract_quiz_data_from_rag_result(self, quiz_result):
        """从RAG引擎结果中提取测验数据，处理可能的错误"""
        try:
            # 如果有quiz_data，直接返回
            if "quiz_data" in quiz_result and quiz_result["quiz_data"]:
                return quiz_result["quiz_data"]

            # 如果有错误但包含text，尝试解析text中的JSON
            if "error" in quiz_result and "text" in quiz_result:
                import json
                import re

                text = quiz_result["text"]

                # 尝试从Markdown代码块中提取JSON
                match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', text)
                json_str = match.group(1) if match else text

                # 移除注释和尾随逗号
                json_str = re.sub(r'//.*?$', '', json_str, flags=re.MULTILINE)
                json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)
                json_str = re.sub(r',(\s*[\]}])', r'\1', json_str)

                try:
                    parsed_data = json.loads(json_str)
                    if isinstance(parsed_data, list):
                        return parsed_data
                    elif isinstance(parsed_data, dict) and "questions" in parsed_data:
                        return parsed_data["questions"]
                except json.JSONDecodeError:
                    pass

            return []

        except Exception as e:
            logger.error(f"提取测验数据失败: {e}")
            return []


@api_view(['POST'])
def quiz_submit(request):
    """提交测验答案"""
    try:
        data = request.data
        quiz_id = data.get('quiz_id')
        answers = data.get('answers', [])
        
        if not quiz_id:
            return Response({'error': '缺少测验ID'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            quiz = Quiz.objects.get(id=quiz_id)
        except Quiz.DoesNotExist:
            return Response({'error': '测验不存在'}, status=status.HTTP_404_NOT_FOUND)
        
        # 创建测验尝试
        attempt = QuizAttempt.objects.create(quiz=quiz)
        
        # 处理答案
        total_score = 0
        total_points = 0
        
        for answer_data in answers:
            question_id = answer_data.get('question_id')
            user_answer = answer_data.get('answer', '')
            
            try:
                question = Question.objects.get(id=question_id, quiz=quiz)
                is_correct = user_answer.strip().lower() == question.correct_answer.strip().lower()
                points_earned = question.points if is_correct else 0
                
                Answer.objects.create(
                    attempt=attempt,
                    question=question,
                    user_answer=user_answer,
                    is_correct=is_correct,
                    points_earned=points_earned
                )
                
                total_score += points_earned
                total_points += question.points
                
            except Question.DoesNotExist:
                continue
        
        # 更新尝试结果
        attempt.score = total_score
        attempt.total_points = total_points
        attempt.is_completed = True
        attempt.save()
        
        return Response({
            'message': '测验提交成功',
            'score': total_score,
            'total_points': total_points,
            'percentage': (total_score / total_points * 100) if total_points > 0 else 0
        })
        
    except Exception as e:
        logger.error(f"测验提交失败: {e}")
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def quiz_history(request):
    """获取测验历史"""
    try:
        attempts = QuizAttempt.objects.filter(is_completed=True)[:20]
        history = []
        
        for attempt in attempts:
            history.append({
                'id': attempt.id,
                'quiz_title': attempt.quiz.title,
                'score': attempt.score,
                'total_points': attempt.total_points,
                'percentage': (attempt.score / attempt.total_points * 100) if attempt.total_points > 0 else 0,
                'completed_at': attempt.completed_at.isoformat() if attempt.completed_at else None
            })
        
        return Response({'history': history})
        
    except Exception as e:
        logger.error(f"获取测验历史失败: {e}")
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def quiz_analysis(request, attempt_id):
    """获取测验分析"""
    try:
        attempt = QuizAttempt.objects.get(id=attempt_id)
        answers = Answer.objects.filter(attempt=attempt)

        analysis = {
            'quiz_title': attempt.quiz.title,
            'total_score': attempt.score,
            'total_points': attempt.total_points,
            'percentage': (attempt.score / attempt.total_points * 100) if attempt.total_points > 0 else 0,
            'questions': []
        }

        for answer in answers:
            question_analysis = {
                'question': answer.question.question_text,
                'user_answer': answer.user_answer,
                'correct_answer': answer.question.correct_answer,
                'is_correct': answer.is_correct,
                'explanation': answer.question.explanation,
                'points_earned': answer.points_earned,
                'total_points': answer.question.points
            }
            analysis['questions'].append(question_analysis)

        return Response(analysis)

    except QuizAttempt.DoesNotExist:
        return Response({'error': '测验尝试不存在'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"获取测验分析失败: {e}")
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@csrf_exempt
@api_view(['POST'])
def document_quiz_generation(request):
    """专门用于文档上传生成测验的API端点"""
    try:
        # 检查是否有文件上传
        if 'file' not in request.FILES:
            return Response({'error': '请上传文档文件'}, status=status.HTTP_400_BAD_REQUEST)

        uploaded_file = request.FILES['file']

        # 获取测验参数
        question_count = int(request.data.get('num', 5))
        difficulty = request.data.get('difficulty', 'medium')
        question_types_str = request.data.get('types', 'MC,TF')
        question_types = question_types_str.split(',') if isinstance(question_types_str, str) else question_types_str
        topic = request.data.get('topic', '')

        logger.info(f"文档测验生成API: 文件={uploaded_file.name}, 题目数量={question_count}")

        # 验证文件类型
        allowed_extensions = ['.pdf', '.docx', '.txt', '.md']
        file_extension = '.' + uploaded_file.name.split('.')[-1].lower()
        if file_extension not in allowed_extensions:
            return Response({
                'error': f'不支持的文件类型。支持的格式: {", ".join(allowed_extensions)}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证文件大小 (最大16MB)
        max_size = 16 * 1024 * 1024  # 16MB
        if uploaded_file.size > max_size:
            return Response({
                'error': f'文件大小超过限制。最大支持 {max_size // (1024*1024)}MB'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 创建文档记录
        document = Document.objects.create(
            title=uploaded_file.name,
            file=uploaded_file,
            file_type=file_extension[1:],  # 去掉点号
            file_size=uploaded_file.size,
            is_processed=False
        )

        logger.info(f"文档创建成功: ID={document.id}, 标题={document.title}")

        # 使用ai_services处理文档
        from inquiryspring_backend.ai_services import process_document_for_rag

        # 处理文档进行RAG
        logger.info(f"开始处理文档进行RAG: {document.id}")
        rag_success = process_document_for_rag(document.id, force_reprocess=True)

        if not rag_success:
            logger.error(f"文档RAG处理失败: {document.id}")
            return Response({'error': '文档处理失败，无法生成测验'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        logger.info(f"文档RAG处理成功: {document.id}")

        # 创建RAG引擎实例并生成测验
        rag_engine = RAGEngine(document_id=document.id)

        user_query = f"基于上传的文档《{document.title}》生成{question_count}道{difficulty}难度的测验题目"
        if topic:
            user_query += f"，重点关注{topic}相关内容"

        logger.info(f"开始生成测验: {user_query}")

        quiz_result = rag_engine.handle_quiz(
            user_query=user_query,
            document_id=document.id,
            question_count=question_count,
            question_types=question_types,
            difficulty=difficulty
        )

        if "error" in quiz_result:
            logger.error(f"测验生成失败: {quiz_result['error']}")
            return Response({'error': quiz_result["error"]}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # 格式化题目
        quiz_data = quiz_result.get("quiz_data", [])
        formatted_questions = []

        for i, q in enumerate(quiz_data):
            formatted_q = {
                'id': i + 1,
                'question': q.get('content', q.get('question_text', q.get('question', ''))),
                'type': q.get('type', q.get('question_type', 'MC')),
                'options': q.get('options', []),
                'correct_answer': q.get('correct_answer', ''),
                'explanation': q.get('explanation', ''),
                'difficulty': q.get('difficulty', difficulty),
                'knowledge_points': q.get('knowledge_points', [])
            }
            formatted_questions.append(formatted_q)

        logger.info(f"基于文档生成测验成功: {len(formatted_questions)}道题目")

        return Response({
            'AIQuestion': formatted_questions,
            'message': f'基于文档《{document.title}》生成测验成功',
            'document_id': document.id,
            'document_title': document.title,
            'question_count': len(formatted_questions),
            'file_size': uploaded_file.size,
            'file_type': file_extension[1:]
        })

    except Exception as e:
        logger.error(f"文档测验生成API失败: {e}")
        return Response({'error': f'生成失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def available_documents(request):
    """获取可用于生成测验的文档列表"""
    try:
        documents = Document.objects.filter(is_processed=True).order_by('-uploaded_at')[:20]

        doc_list = []
        for doc in documents:
            doc_list.append({
                'id': doc.id,
                'title': doc.title,
                'file_type': doc.file_type,
                'file_size': doc.file_size,
                'uploaded_at': doc.uploaded_at.isoformat(),
                'processed_at': doc.processed_at.isoformat() if doc.processed_at else None
            })

        return Response({
            'documents': doc_list,
            'count': len(doc_list)
        })

    except Exception as e:
        logger.error(f"获取文档列表失败: {e}")
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
