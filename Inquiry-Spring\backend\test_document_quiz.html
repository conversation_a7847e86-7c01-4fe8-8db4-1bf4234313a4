<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档上传生成测验 - 测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .question {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .question-header {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .options {
            margin: 10px 0;
        }
        .option {
            margin: 5px 0;
            padding: 5px 10px;
            background: white;
            border-radius: 3px;
        }
        .correct-answer {
            color: #28a745;
            font-weight: bold;
        }
        .explanation {
            color: #6c757d;
            font-style: italic;
            margin-top: 10px;
        }
        .loading {
            text-align: center;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 文档上传生成测验</h1>
        
        <form id="quizForm" enctype="multipart/form-data">
            <div class="form-group">
                <label for="file">选择文档文件:</label>
                <input type="file" id="file" name="file" accept=".pdf,.docx,.txt,.md" required>
                <small>支持格式: PDF, DOCX, TXT, MD (最大16MB)</small>
            </div>
            
            <div class="form-group">
                <label for="num">题目数量:</label>
                <select id="num" name="num">
                    <option value="3">3题</option>
                    <option value="5" selected>5题</option>
                    <option value="8">8题</option>
                    <option value="10">10题</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="difficulty">难度级别:</label>
                <select id="difficulty" name="difficulty">
                    <option value="easy">简单</option>
                    <option value="medium" selected>中等</option>
                    <option value="hard">困难</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="types">题目类型:</label>
                <select id="types" name="types" multiple>
                    <option value="MC" selected>单选题</option>
                    <option value="TF" selected>判断题</option>
                    <option value="SA">简答题</option>
                    <option value="FB">填空题</option>
                </select>
                <small>按住Ctrl键可多选</small>
            </div>
            
            <div class="form-group">
                <label for="topic">重点关注内容 (可选):</label>
                <input type="text" id="topic" name="topic" placeholder="例如：机器学习算法、数据结构等">
            </div>
            
            <button type="submit" id="submitBtn">🚀 生成测验</button>
            <button type="button" id="testBtn">🧪 使用示例文档测试</button>
        </form>
        
        <div id="result" class="result">
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        
        document.getElementById('quizForm').addEventListener('submit', function(e) {
            e.preventDefault();
            generateQuiz();
        });
        
        document.getElementById('testBtn').addEventListener('click', function() {
            testWithSampleDocument();
        });
        
        async function generateQuiz() {
            const submitBtn = document.getElementById('submitBtn');
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            // 检查文件
            const fileInput = document.getElementById('file');
            if (!fileInput.files[0]) {
                showError('请选择一个文档文件');
                return;
            }
            
            // 准备表单数据
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('num', document.getElementById('num').value);
            formData.append('difficulty', document.getElementById('difficulty').value);
            
            // 处理题目类型
            const typeSelect = document.getElementById('types');
            const selectedTypes = Array.from(typeSelect.selectedOptions).map(option => option.value);
            formData.append('types', selectedTypes.join(','));
            
            formData.append('topic', document.getElementById('topic').value);
            
            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.textContent = '🔄 生成中...';
            showLoading('正在处理文档并生成测验，请稍候...');
            
            try {
                const response = await fetch(`${API_BASE}/test/document-quiz/`, {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showSuccess(data);
                } else {
                    showError(data.error || '生成失败');
                }
                
            } catch (error) {
                showError('网络错误: ' + error.message);
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '🚀 生成测验';
            }
        }
        
        async function testWithSampleDocument() {
            const testBtn = document.getElementById('testBtn');
            
            testBtn.disabled = true;
            testBtn.textContent = '🔄 测试中...';
            showLoading('使用示例文档生成测验...');
            
            try {
                // 创建示例文档内容
                const sampleContent = `
机器学习基础知识

机器学习是人工智能的一个重要分支，它使计算机能够在没有明确编程的情况下学习和改进。

主要类型：
1. 监督学习：使用标记数据训练模型，如分类和回归问题。
2. 无监督学习：从未标记数据中发现模式，如聚类和降维。
3. 强化学习：通过与环境交互学习最优策略。

常用算法：
- 线性回归：用于预测连续值
- 决策树：易于理解和解释的分类算法
- 神经网络：模拟人脑神经元的复杂模型
- 支持向量机：在高维空间中寻找最优分类边界
                `;
                
                const blob = new Blob([sampleContent], { type: 'text/plain' });
                const file = new File([blob], '机器学习基础.txt', { type: 'text/plain' });
                
                const formData = new FormData();
                formData.append('file', file);
                formData.append('num', '5');
                formData.append('difficulty', 'medium');
                formData.append('types', 'MC,TF');
                formData.append('topic', '机器学习算法');
                
                const response = await fetch(`${API_BASE}/test/document-quiz/`, {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showSuccess(data);
                } else {
                    showError(data.error || '测试失败');
                }
                
            } catch (error) {
                showError('测试错误: ' + error.message);
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '🧪 使用示例文档测试';
            }
        }
        
        function showLoading(message) {
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            resultContent.innerHTML = `<div class="loading">${message}</div>`;
            resultDiv.className = 'result';
            resultDiv.style.display = 'block';
        }
        
        function showSuccess(data) {
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            let html = `
                <h3>✅ ${data.message}</h3>
                <p><strong>文档:</strong> ${data.document_title}</p>
                <p><strong>题目数量:</strong> ${data.question_count}</p>
                <p><strong>文件大小:</strong> ${(data.file_size / 1024).toFixed(1)} KB</p>
                <hr>
            `;
            
            if (data.AIQuestion && data.AIQuestion.length > 0) {
                html += '<h4>📋 生成的测验题目:</h4>';
                
                data.AIQuestion.forEach((question, index) => {
                    html += `
                        <div class="question">
                            <div class="question-header">
                                题目 ${index + 1} [${question.type}] - ${question.difficulty || '中等'}
                            </div>
                            <div><strong>问题:</strong> ${question.question}</div>
                    `;
                    
                    if (question.options && question.options.length > 0) {
                        html += '<div class="options"><strong>选项:</strong>';
                        question.options.forEach((option, i) => {
                            html += `<div class="option">${String.fromCharCode(65 + i)}. ${option}</div>`;
                        });
                        html += '</div>';
                    }
                    
                    html += `
                            <div class="correct-answer"><strong>正确答案:</strong> ${question.correct_answer}</div>
                    `;
                    
                    if (question.explanation) {
                        html += `<div class="explanation"><strong>解析:</strong> ${question.explanation}</div>`;
                    }
                    
                    html += '</div>';
                });
            }
            
            resultContent.innerHTML = html;
            resultDiv.className = 'result success';
            resultDiv.style.display = 'block';
        }
        
        function showError(message) {
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            resultContent.innerHTML = `<h3>❌ 错误</h3><p>${message}</p>`;
            resultDiv.className = 'result error';
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
