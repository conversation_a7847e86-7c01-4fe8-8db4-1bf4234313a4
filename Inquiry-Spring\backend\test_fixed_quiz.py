#!/usr/bin/env python
"""
测试修复后的测验生成功能
"""

import requests
import json
import time

# 测试配置
BASE_URL = "http://localhost:8000"

def test_document_upload_quiz():
    """测试文档上传生成测验"""
    print("🧪 测试文档上传生成测验...")
    
    # 创建测试文档内容
    test_content = """
    湖北省大学生竞赛奖励加分规则详解

    根据湖北省教育厅最新发布的《湖北省大学生竞赛奖励加分实施办法》，参加各类学科竞赛获奖的大学生可获得相应的加分奖励。

    一、省级竞赛加分标准

    1. 湖北省"挑战杯"大学生课外学术科技作品竞赛
       - 特等奖：加6分
       - 一等奖：加5分
       - 二等奖：加3分
       - 三等奖：加2分

    2. 全国大学生数学竞赛湖北赛区
       - 一等奖：加4分
       - 二等奖：加2分
       - 三等奖：加1分

    3. 湖北省大学生工程训练综合能力竞赛
       - 一等奖：加4分
       - 二等奖：加2分
       - 三等奖：加1分

    4. 湖北省大学生社会调查大赛
       - 一等奖：加4分
       - 二等奖：加2分
       - 三等奖：加1分

    二、国家级竞赛加分标准

    1. 全国"挑战杯"大学生课外学术科技作品竞赛
       - 特等奖：加10分
       - 一等奖：加8分
       - 二等奖：加6分
       - 三等奖：加4分

    2. 全国大学生数学建模竞赛
       - 一等奖：加8分
       - 二等奖：加6分
       - 三等奖：加4分

    三、加分规则说明

    1. 同一竞赛项目只能申请一次加分
    2. 团队项目按个人贡献度分配加分
    3. 加分需提供获奖证书原件和复印件
    4. 加分申请需在获奖后一年内提出
    5. 最高累计加分不超过20分

    四、申请流程

    1. 填写《湖北省大学生竞赛加分申请表》
    2. 提交获奖证书原件及复印件
    3. 学院初审并签署意见
    4. 学校教务处复审
    5. 公示无异议后正式加分

    五、注意事项

    - 虚假申报将取消加分资格并记入诚信档案
    - 加分仅适用于保研、奖学金评定等学术评价
    - 具体实施细则以各高校规定为准
    """
    
    url = f"{BASE_URL}/api/test/"
    
    files = {
        'file': ('湖北省竞赛加分规则详解.txt', test_content.encode('utf-8'), 'text/plain')
    }
    
    data = {
        'num': '3',
        'difficulty': 'medium',
        'types': 'MC,TF',
        'topic': '竞赛加分规则'
    }
    
    try:
        print("📤 发送文档上传请求...")
        response = requests.post(url, files=files, data=data, timeout=120)
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 文档上传测验生成成功!")
            print(f"📄 文档: {result.get('document_title', '未知')}")
            print(f"📊 生成题目数量: {len(result.get('AIQuestion', []))}")
            
            # 显示生成的题目
            for i, question in enumerate(result.get('AIQuestion', []), 1):
                print(f"\n📋 题目 {i}:")
                print(f"   类型: {question.get('type', '未知')}")
                print(f"   内容: {question.get('question', '')}")
                if question.get('options'):
                    print(f"   选项: {question['options']}")
                print(f"   正确答案: {question.get('correct_answer', '')}")
                if question.get('explanation'):
                    print(f"   解析: {question.get('explanation', '')}")
                    
            # 检查是否使用了备用方案
            if result.get('note'):
                print(f"📝 备注: {result['note']}")
                
        else:
            print(f"❌ 文档上传测验生成失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误信息: {error_data.get('error', response.text)}")
            except:
                print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def view_latest_debug_info():
    """查看最新的调试信息"""
    print("\n🔍 查看最新调试信息...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/test/debug/latest/", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            debug_info = data.get('debug_info', {})
            analysis = data.get('analysis', {})
            
            print("📄 最新调试信息:")
            print(f"   文件名: {data.get('filename', '未知')}")
            print(f"   请求类型: {debug_info.get('request_type', '未知')}")
            print(f"   文档标题: {debug_info.get('document_title', '无')}")
            print(f"   题目数量: {debug_info.get('question_count', '未知')}")
            print(f"   难度: {debug_info.get('difficulty', '未知')}")
            print(f"   是否有错误: {'是' if analysis.get('has_error') else '否'}")
            print(f"   测验数据数量: {analysis.get('quiz_data_count', 0)}")
            
            if analysis.get('has_error'):
                print(f"   ❌ 错误信息: {analysis.get('error_message', '未知')}")
            
            if analysis.get('text_preview'):
                print(f"   📝 AI响应预览:")
                print(f"   {analysis['text_preview'][:300]}...")
                
            # 显示原始响应的关键信息
            raw_response = debug_info.get('raw_ai_response', {})
            if 'text' in raw_response:
                print(f"\n🤖 AI原始响应包含文本: {len(raw_response['text'])} 字符")
            if 'quiz_data' in raw_response:
                print(f"🤖 AI原始响应包含quiz_data: {len(raw_response.get('quiz_data', []))} 项")
                
        elif response.status_code == 404:
            print("📄 没有找到调试日志")
        else:
            print(f"❌ 获取调试信息失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 获取调试信息异常: {e}")

def main():
    """主测试函数"""
    print("🔍 开始测试修复后的测验生成功能")
    print(f"目标服务器: {BASE_URL}")
    print("=" * 60)
    
    # 测试文档上传生成测验
    test_document_upload_quiz()
    
    # 等待一下，让调试信息保存
    time.sleep(3)
    
    # 查看调试信息
    view_latest_debug_info()
    
    print("\n" + "=" * 60)
    print("🔍 测试完成")
    print("\n📋 修复要点:")
    print("1. ✅ 修复了 'list' object has no attribute 'strip' 错误")
    print("2. ✅ 改进了题目类型映射（SA -> MC）")
    print("3. ✅ 增强了选项和答案的格式处理")
    print("4. ✅ 备用方案现在会提供实际文档内容给AI")
    print("5. ✅ 调试信息正确保存，便于问题诊断")

if __name__ == "__main__":
    main()
