#!/usr/bin/env python
"""
测试修复后的测验生成功能
"""

import requests
import json

# 测试配置
BASE_URL = "http://localhost:8000"

def test_quiz_generation():
    """测试基于现有文档的测验生成"""
    print("🧪 测试基于现有文档的测验生成...")
    
    url = f"{BASE_URL}/api/test/"
    data = {
        "num": 2,
        "difficulty": "medium",
        "types": ["MC", "TF"],
        "topic": "文档内容"
    }
    
    try:
        response = requests.post(url, json=data, timeout=60)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 测验生成成功!")
            print(f"生成题目数量: {len(result.get('AIQuestion', []))}")
            
            for i, question in enumerate(result.get('AIQuestion', []), 1):
                print(f"\n题目 {i}:")
                print(f"  类型: {question.get('type', '未知')}")
                print(f"  内容: {question.get('question', '')[:100]}...")
                print(f"  选项数量: {len(question.get('options', []))}")
                print(f"  正确答案: {question.get('correct_answer', '')}")
        else:
            print(f"❌ 测验生成失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_document_upload_quiz():
    """测试文档上传生成测验"""
    print("\n🧪 测试文档上传生成测验...")
    
    # 创建测试文档
    test_content = """
    Python编程基础

    Python是一种高级编程语言，具有简洁的语法和强大的功能。

    主要特点：
    1. 简洁易读的语法
    2. 丰富的标准库
    3. 跨平台兼容性
    4. 面向对象编程支持

    基本数据类型：
    - 整数 (int)
    - 浮点数 (float)
    - 字符串 (str)
    - 布尔值 (bool)
    - 列表 (list)
    - 字典 (dict)

    控制结构：
    - if/elif/else 条件语句
    - for/while 循环语句
    - try/except 异常处理

    Python广泛应用于Web开发、数据分析、人工智能等领域。
    """
    
    url = f"{BASE_URL}/api/test/document-quiz/"
    
    files = {
        'file': ('python_basics.txt', test_content.encode('utf-8'), 'text/plain')
    }
    
    data = {
        'num': '3',
        'difficulty': 'medium',
        'types': 'MC,TF',
        'topic': 'Python编程'
    }
    
    try:
        response = requests.post(url, files=files, data=data, timeout=120)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 文档上传测验生成成功!")
            print(f"文档: {result.get('document_title', '未知')}")
            print(f"生成题目数量: {result.get('question_count', 0)}")
            
            for i, question in enumerate(result.get('AIQuestion', []), 1):
                print(f"\n题目 {i}:")
                print(f"  类型: {question.get('type', '未知')}")
                print(f"  内容: {question.get('question', '')[:100]}...")
                if question.get('options'):
                    print(f"  选项: {', '.join(question['options'][:2])}...")
                print(f"  正确答案: {question.get('correct_answer', '')}")
        else:
            print(f"❌ 文档上传测验生成失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def main():
    """主测试函数"""
    print("🔍 开始测试修复后的测验生成功能")
    print(f"目标服务器: {BASE_URL}")
    
    # 测试1: 基于现有文档生成测验
    test_quiz_generation()
    
    # 测试2: 文档上传生成测验
    test_document_upload_quiz()
    
    print("\n🔍 测试完成")
    print("\n📋 如果看到 ✅ 表示功能正常")
    print("📋 如果看到 ❌ 表示还有问题需要修复")

if __name__ == "__main__":
    main()
