#!/usr/bin/env python
"""
测试修复后的测验生成功能，包含调试信息查看
"""

import requests
import json
import time

# 测试配置
BASE_URL = "http://localhost:8000"

def test_document_upload_quiz():
    """测试文档上传生成测验"""
    print("🧪 测试文档上传生成测验...")
    
    # 创建测试文档
    test_content = """
    湖北省大学生竞赛奖励加分规则

    根据湖北省教育厅相关规定，大学生参加各类竞赛获奖可获得相应加分：

    一、省级竞赛加分标准：
    1. 湖北省"挑战杯"大学生课外学术科技作品竞赛
       - 一等奖：加5分
       - 二等奖：加3分  
       - 三等奖：加2分

    2. 全国大学生数学竞赛湖北赛区
       - 一等奖：加4分
       - 二等奖：加2分
       - 三等奖：加1分

    3. 湖北省大学生工程训练综合能力竞赛
       - 一等奖：加4分
       - 二等奖：加2分
       - 三等奖：加1分

    4. 湖北省大学生社会调查大赛
       - 一等奖：加4分
       - 二等奖：加2分
       - 三等奖：加1分

    二、国家级竞赛加分标准：
    1. 全国"挑战杯"大学生课外学术科技作品竞赛
       - 特等奖：加10分
       - 一等奖：加8分
       - 二等奖：加6分
       - 三等奖：加4分

    注意事项：
    - 同一竞赛项目只能申请一次加分
    - 团队项目按个人贡献度分配加分
    - 加分需提供获奖证书原件
    """
    
    url = f"{BASE_URL}/api/test/"
    
    files = {
        'file': ('湖北省竞赛加分规则.txt', test_content.encode('utf-8'), 'text/plain')
    }
    
    data = {
        'num': '5',
        'difficulty': 'medium',
        'types': 'MC,TF',
        'topic': '竞赛加分规则'
    }
    
    try:
        print("📤 发送文档上传请求...")
        response = requests.post(url, files=files, data=data, timeout=120)
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 文档上传测验生成成功!")
            print(f"📄 文档: {result.get('document_title', '未知')}")
            print(f"📊 生成题目数量: {len(result.get('AIQuestion', []))}")
            
            # 显示生成的题目
            for i, question in enumerate(result.get('AIQuestion', []), 1):
                print(f"\n📋 题目 {i}:")
                print(f"   类型: {question.get('type', '未知')}")
                print(f"   内容: {question.get('question', '')}")
                if question.get('options'):
                    print(f"   选项: {question['options']}")
                print(f"   正确答案: {question.get('correct_answer', '')}")
                if question.get('explanation'):
                    print(f"   解析: {question.get('explanation', '')}")
        else:
            print(f"❌ 文档上传测验生成失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_normal_quiz():
    """测试基于现有文档的测验生成"""
    print("\n🧪 测试基于现有文档的测验生成...")
    
    url = f"{BASE_URL}/api/test/"
    data = {
        "num": 3,
        "difficulty": "medium",
        "types": ["MC", "TF"],
        "topic": "竞赛加分"
    }
    
    try:
        print("📤 发送普通测验请求...")
        response = requests.post(url, json=data, timeout=60)
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 普通测验生成成功!")
            print(f"📊 生成题目数量: {len(result.get('AIQuestion', []))}")
            
            # 显示生成的题目
            for i, question in enumerate(result.get('AIQuestion', []), 1):
                print(f"\n📋 题目 {i}:")
                print(f"   类型: {question.get('type', '未知')}")
                print(f"   内容: {question.get('question', '')}")
                if question.get('options'):
                    print(f"   选项: {question['options']}")
                print(f"   正确答案: {question.get('correct_answer', '')}")
        else:
            print(f"❌ 普通测验生成失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def view_debug_info():
    """查看调试信息"""
    print("\n🔍 查看最新调试信息...")
    
    try:
        # 获取最新调试信息
        response = requests.get(f"{BASE_URL}/api/test/debug/latest/", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            debug_info = data.get('debug_info', {})
            analysis = data.get('analysis', {})
            
            print("📄 最新调试信息:")
            print(f"   文件名: {data.get('filename', '未知')}")
            print(f"   请求类型: {debug_info.get('request_type', '未知')}")
            print(f"   文档标题: {debug_info.get('document_title', '无')}")
            print(f"   题目数量: {debug_info.get('question_count', '未知')}")
            print(f"   难度: {debug_info.get('difficulty', '未知')}")
            print(f"   是否有错误: {'是' if analysis.get('has_error') else '否'}")
            print(f"   测验数据数量: {analysis.get('quiz_data_count', 0)}")
            
            if analysis.get('has_error'):
                print(f"   ❌ 错误信息: {analysis.get('error_message', '未知')}")
            
            if analysis.get('text_preview'):
                print(f"   📝 AI响应预览: {analysis['text_preview'][:200]}...")
                
        elif response.status_code == 404:
            print("📄 没有找到调试日志")
        else:
            print(f"❌ 获取调试信息失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 获取调试信息异常: {e}")

def view_debug_logs():
    """查看调试日志列表"""
    print("\n📋 查看调试日志列表...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/test/debug/logs/", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            logs = data.get('logs', [])
            
            print(f"📊 共找到 {data.get('total_count', 0)} 个日志文件")
            
            for i, log in enumerate(logs[:5], 1):  # 只显示前5个
                print(f"\n📄 日志 {i}:")
                print(f"   文件名: {log.get('filename', '未知')}")
                print(f"   请求类型: {log.get('request_type', '未知')}")
                print(f"   文档标题: {log.get('document_title', '无')}")
                print(f"   时间戳: {log.get('timestamp', '无')}")
                print(f"   是否有错误: {'是' if log.get('has_error') else '否'}")
                print(f"   文件大小: {log.get('file_size', 0) / 1024:.1f} KB")
                
        else:
            print(f"❌ 获取日志列表失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 获取日志列表异常: {e}")

def main():
    """主测试函数"""
    print("🔍 开始测试修复后的测验生成功能（包含调试）")
    print(f"目标服务器: {BASE_URL}")
    
    # 测试1: 文档上传生成测验
    test_document_upload_quiz()
    
    # 等待一下，让调试信息保存
    time.sleep(2)
    
    # 测试2: 基于现有文档生成测验
    test_normal_quiz()
    
    # 等待一下，让调试信息保存
    time.sleep(2)
    
    # 查看调试信息
    view_debug_info()
    view_debug_logs()
    
    print("\n🔍 测试完成")
    print("\n📋 检查要点:")
    print("1. 文档上传是否成功（即使RAG失败也应该有备用方案）")
    print("2. 普通测验是否正常工作")
    print("3. 调试信息是否正确保存")
    print("4. 错误恢复机制是否有效")

if __name__ == "__main__":
    main()
