#!/usr/bin/env python
"""
查看AI服务调试日志的工具
"""

import os
import json
import glob
from datetime import datetime

def view_latest_debug_logs(log_type=None, count=5):
    """查看最新的调试日志"""
    
    # 调试日志目录
    debug_dir = os.path.join(os.path.dirname(__file__), 'debug_logs')
    
    if not os.path.exists(debug_dir):
        print("❌ 调试日志目录不存在")
        return
    
    # 查找日志文件
    if log_type:
        pattern = os.path.join(debug_dir, f"{log_type}_*.json")
    else:
        pattern = os.path.join(debug_dir, "*.json")
    
    log_files = glob.glob(pattern)
    
    if not log_files:
        print("❌ 没有找到调试日志文件")
        return
    
    # 按修改时间排序
    log_files.sort(key=os.path.getmtime, reverse=True)
    
    print(f"📋 找到 {len(log_files)} 个调试日志文件")
    print(f"📋 显示最新的 {min(count, len(log_files))} 个文件\n")
    
    for i, log_file in enumerate(log_files[:count]):
        print(f"{'='*80}")
        print(f"📄 文件 {i+1}: {os.path.basename(log_file)}")
        print(f"🕒 修改时间: {datetime.fromtimestamp(os.path.getmtime(log_file))}")
        print(f"{'='*80}")
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 显示基本信息
            print(f"📝 请求类型: {data.get('request_type', '未知')}")
            print(f"📄 文档标题: {data.get('document_title', '无')}")
            print(f"🔢 文档ID: {data.get('document_id', '无')}")
            print(f"❓ 用户查询: {data.get('user_query', '无')}")
            print(f"📊 题目数量: {data.get('question_count', '无')}")
            print(f"⚡ 难度: {data.get('difficulty', '无')}")
            print(f"📋 题目类型: {data.get('question_types', '无')}")
            print(f"🕒 时间戳: {data.get('timestamp', '无')}")
            
            # 显示AI响应信息
            raw_response = data.get('raw_ai_response', {})
            print(f"\n🤖 AI响应信息:")
            print(f"   ✅ 是否有错误: {'是' if 'error' in raw_response else '否'}")
            if 'error' in raw_response:
                print(f"   ❌ 错误信息: {raw_response['error']}")
            
            print(f"   📝 是否有文本: {'是' if 'text' in raw_response else '否'}")
            if 'text' in raw_response:
                text_preview = raw_response['text'][:200] + "..." if len(raw_response['text']) > 200 else raw_response['text']
                print(f"   📝 文本预览: {text_preview}")
            
            print(f"   📊 是否有quiz_data: {'是' if 'quiz_data' in raw_response else '否'}")
            if 'quiz_data' in raw_response:
                quiz_data = raw_response['quiz_data']
                print(f"   📊 quiz_data类型: {type(quiz_data)}")
                if isinstance(quiz_data, list):
                    print(f"   📊 题目数量: {len(quiz_data)}")
                    for j, q in enumerate(quiz_data[:3]):  # 只显示前3题
                        print(f"      题目 {j+1}: {str(q)[:100]}...")
                else:
                    print(f"   📊 quiz_data内容: {str(quiz_data)[:200]}...")
            
            # 显示其他字段
            other_fields = [k for k in raw_response.keys() if k not in ['error', 'text', 'quiz_data']]
            if other_fields:
                print(f"   🔧 其他字段: {other_fields}")
            
            print(f"\n📄 完整原始响应:")
            print(json.dumps(raw_response, ensure_ascii=False, indent=2)[:1000] + "..." if len(str(raw_response)) > 1000 else json.dumps(raw_response, ensure_ascii=False, indent=2))
            
        except Exception as e:
            print(f"❌ 读取日志文件失败: {e}")
        
        print(f"\n")

def analyze_quiz_errors():
    """分析测验生成中的常见错误"""
    debug_dir = os.path.join(os.path.dirname(__file__), 'debug_logs')
    
    if not os.path.exists(debug_dir):
        print("❌ 调试日志目录不存在")
        return
    
    log_files = glob.glob(os.path.join(debug_dir, "*quiz*.json"))
    
    if not log_files:
        print("❌ 没有找到测验相关的调试日志")
        return
    
    print(f"🔍 分析 {len(log_files)} 个测验日志文件\n")
    
    error_count = 0
    success_count = 0
    common_errors = {}
    
    for log_file in log_files:
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            raw_response = data.get('raw_ai_response', {})
            
            if 'error' in raw_response:
                error_count += 1
                error_msg = raw_response['error']
                if error_msg in common_errors:
                    common_errors[error_msg] += 1
                else:
                    common_errors[error_msg] = 1
            else:
                success_count += 1
                
        except Exception as e:
            print(f"❌ 分析文件失败 {log_file}: {e}")
    
    print(f"📊 统计结果:")
    print(f"   ✅ 成功: {success_count}")
    print(f"   ❌ 失败: {error_count}")
    print(f"   📈 成功率: {success_count/(success_count+error_count)*100:.1f}%")
    
    if common_errors:
        print(f"\n🔍 常见错误:")
        for error, count in sorted(common_errors.items(), key=lambda x: x[1], reverse=True):
            print(f"   {count}次: {error}")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "latest":
            count = int(sys.argv[2]) if len(sys.argv) > 2 else 3
            view_latest_debug_logs(count=count)
        elif command == "quiz":
            count = int(sys.argv[2]) if len(sys.argv) > 2 else 3
            view_latest_debug_logs(log_type="quiz", count=count)
        elif command == "document":
            count = int(sys.argv[2]) if len(sys.argv) > 2 else 3
            view_latest_debug_logs(log_type="document_quiz", count=count)
        elif command == "analyze":
            analyze_quiz_errors()
        else:
            print("❌ 未知命令")
            print("用法:")
            print("  python view_debug_logs.py latest [数量]     - 查看最新日志")
            print("  python view_debug_logs.py quiz [数量]       - 查看测验日志")
            print("  python view_debug_logs.py document [数量]   - 查看文档测验日志")
            print("  python view_debug_logs.py analyze          - 分析错误统计")
    else:
        print("🔍 AI服务调试日志查看器")
        print("\n用法:")
        print("  python view_debug_logs.py latest [数量]     - 查看最新日志")
        print("  python view_debug_logs.py quiz [数量]       - 查看测验日志")
        print("  python view_debug_logs.py document [数量]   - 查看文档测验日志")
        print("  python view_debug_logs.py analyze          - 分析错误统计")
        print("\n示例:")
        print("  python view_debug_logs.py latest 5")
        print("  python view_debug_logs.py quiz 3")
        print("  python view_debug_logs.py analyze")

if __name__ == "__main__":
    main()
