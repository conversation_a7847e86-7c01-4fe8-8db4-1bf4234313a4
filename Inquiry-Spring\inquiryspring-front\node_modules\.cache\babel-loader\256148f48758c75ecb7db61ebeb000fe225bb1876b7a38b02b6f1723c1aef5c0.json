{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport axios from 'axios';\nimport { Marked } from 'marked';\nimport { markedHighlight } from \"marked-highlight\";\nimport hljs from 'highlight.js/lib/core';\nimport javascript from 'highlight.js/lib/languages/javascript';\nimport python from 'highlight.js/lib/languages/python';\nimport java from 'highlight.js/lib/languages/java';\nimport xml from 'highlight.js/lib/languages/xml';\nimport json from 'highlight.js/lib/languages/json';\nimport css from 'highlight.js/lib/languages/css';\nimport markdown from 'highlight.js/lib/languages/markdown';\nimport bash from 'highlight.js/lib/languages/bash';\nimport 'highlight.js/styles/github.css'; // 推荐风格，可换为其它\n\n// 注册常用语言\nhljs.registerLanguage('javascript', javascript);\nhljs.registerLanguage('python', python);\nhljs.registerLanguage('java', java);\nhljs.registerLanguage('xml', xml);\nhljs.registerLanguage('json', json);\nhljs.registerLanguage('css', css);\nhljs.registerLanguage('markdown', markdown);\nhljs.registerLanguage('bash', bash);\nexport default {\n  data() {\n    return {\n      url: this.HOST + '/chat/',\n      inputMessage: '',\n      messages: [],\n      form: {\n        message: '',\n        timestamp: ''\n      },\n      isWaitingForAI: false,\n      currentSessionId: null,\n      pollingTimer: null,\n      selectedFile: null,\n      selectedFileName: '',\n      // 新增：存储选中文件名\n      currentDocumentId: null,\n      // 当前文档ID\n\n      isLoading: false,\n      // 加载状态\n      username: '',\n      userInitial: ''\n    };\n  },\n  created() {\n    // 检查localStorage中是否有用户信息\n    const userInfo = localStorage.getItem('userInfo');\n    // 将JSON字符串转换为对象\n    const parsedUserInfo = JSON.parse(userInfo);\n    // 触发Vuex action来更新store中的用户信息\n    this.$store.dispatch('restoreUserInfo', parsedUserInfo);\n\n    // 页面加载时从store恢复历史\n    const history = this.$store.getters.getChatHistory;\n    if (history && Array.isArray(history) && history.length > 0) {\n      this.messages = history.map(msg => ({\n        ...msg,\n        timestamp: new Date(msg.timestamp)\n      }));\n    }\n\n    // 获取当前用户信息\n    const user = this.$store.getters.getUserInfo;\n    if (user && user.username) {\n      this.username = user.username;\n      this.userInitial = user.username.charAt(0).toUpperCase();\n    } else {\n      this.username = '未登录';\n      this.userInitial = '?';\n    }\n  },\n  mounted() {\n    // 页面初次渲染后自动滚动到底部\n    this.$nextTick(() => {\n      const container = document.querySelector('.message-list');\n      if (container) container.scrollTop = container.scrollHeight;\n    });\n  },\n  beforeDestroy() {\n    // 组件销毁前清理轮询\n    this.stopPolling();\n  },\n  computed: {\n    // 删除了动画相关的computed属性\n  },\n  watch: {\n    messages: {\n      handler(newVal) {\n        // 每次对话变更都保存到store\n        this.$store.dispatch('updateChatHistory', newVal.map(msg => ({\n          ...msg,\n          timestamp: msg.timestamp instanceof Date ? msg.timestamp.toISOString() : msg.timestamp\n        })));\n      },\n      deep: true\n    }\n  },\n  methods: {\n    markdownToHtml(message) {\n      if (!message) return '';\n      const marked = new Marked(markedHighlight({\n        pedantic: false,\n        gfm: true,\n        breaks: true,\n        smartLists: true,\n        xhtml: true,\n        async: false,\n        langPrefix: 'hljs language-',\n        emptyLangClass: 'no-lang',\n        highlight: (code, lang) => {\n          if (lang && hljs.getLanguage(lang)) {\n            return hljs.highlight(code, {\n              language: lang\n            }).value;\n          }\n          return hljs.highlightAuto(code).value;\n        }\n      }));\n      return marked.parse(message);\n    },\n    sendMessage() {\n      if (this.inputMessage.trim() === '') return;\n      this.form.message = this.inputMessage;\n      this.form.timestamp = new Date();\n\n      // 添加用户消息\n      const userMsg = {\n        text: this.inputMessage,\n        isUser: true,\n        timestamp: this.form.timestamp\n      };\n      this.messages.push(userMsg);\n\n      // 同步到store\n      this.$store.dispatch('addChatMessage', {\n        ...userMsg,\n        timestamp: userMsg.timestamp.toISOString()\n      });\n      this.inputMessage = '';\n      this.$nextTick(() => {\n        const container = document.querySelector('.message-list');\n        if (container) container.scrollTop = container.scrollHeight;\n      });\n\n      // 显示等待状态\n      this.isWaitingForAI = true;\n\n      // 发送消息到后端API\n      axios.post(this.url, this.form).then(response => {\n        console.log('消息发送成功:', response.data);\n        if (response.data.session_id) {\n          this.currentSessionId = response.data.session_id;\n          // 开始轮询检查状态\n          this.startPolling();\n        } else {\n          this.isWaitingForAI = false;\n          this.$message.error('未获取到会话ID');\n        }\n      }).catch(error => {\n        console.error('发送失败:', error);\n        this.$message.error('发送失败：' + error.message);\n        this.isWaitingForAI = false;\n      });\n    },\n    startPolling() {\n      if (this.pollingTimer) {\n        clearInterval(this.pollingTimer);\n      }\n      this.pollingTimer = setInterval(() => {\n        this.checkMessageStatus();\n      }, 1000); // 每秒检查一次\n    },\n    checkMessageStatus() {\n      if (!this.currentSessionId) return;\n      axios.get(`${this.HOST}/chat/status/${this.currentSessionId}/`).then(response => {\n        console.log('状态检查:', response.data);\n        if (response.data.is_ready) {\n          // 消息已完成\n          this.stopPolling();\n          this.isWaitingForAI = false;\n\n          // 添加AI回复到消息列表\n          const aiMsg = {\n            text: response.data.ai_response,\n            isUser: false,\n            timestamp: new Date()\n          };\n          this.messages.push(aiMsg);\n\n          // 同步到store\n          this.$store.dispatch('addChatMessage', {\n            ...aiMsg,\n            timestamp: aiMsg.timestamp.toISOString()\n          });\n          this.$nextTick(() => {\n            const container = document.querySelector('.message-list');\n            if (container) container.scrollTop = container.scrollHeight;\n          });\n          this.currentSessionId = null;\n        }\n      }).catch(error => {\n        console.error('状态检查失败:', error);\n        // 继续轮询，不中断\n      });\n    },\n    stopPolling() {\n      if (this.pollingTimer) {\n        clearInterval(this.pollingTimer);\n        this.pollingTimer = null;\n      }\n    },\n    formatTime(date) {\n      return `${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`;\n    },\n    gotoSummarize() {\n      this.$router.push({\n        path: '/summarize'\n      });\n    },\n    gotoTest() {\n      this.$router.push({\n        path: '/test'\n      });\n    },\n    gotoPrj() {\n      this.$router.push({\n        path: '/project'\n      });\n    },\n    triggerFileInput() {\n      this.$refs.fileInput.click();\n    },\n    async handleFileChange(event) {\n      const file = event.target.files[0];\n      if (file) {\n        this.selectedFile = file;\n        this.selectedFileName = file.name;\n\n        // 自动上传文件\n        await this.uploadDocument();\n      } else {\n        this.selectedFile = null;\n        this.selectedFileName = '';\n        this.currentDocumentId = null;\n      }\n    },\n    async uploadDocument() {\n      if (!this.selectedFile) return;\n      const formData = new FormData();\n      formData.append('file', this.selectedFile);\n      try {\n        this.isLoading = true;\n        const response = await axios.post(this.HOST + '/chat/upload/', formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        if (response.data.document_id) {\n          this.currentDocumentId = response.data.document_id;\n\n          // 添加系统消息到聊天记录\n          const systemMsg = {\n            text: `📄 文档 \"${response.data.filename}\" 上传成功！现在您可以基于这个文档进行问答。`,\n            isUser: false,\n            timestamp: new Date(),\n            isSystem: true\n          };\n          this.messages.push(systemMsg);\n\n          // 同步到store\n          this.$store.dispatch('addChatMessage', {\n            ...systemMsg,\n            timestamp: systemMsg.timestamp.toISOString()\n          });\n          this.$nextTick(() => {\n            const container = document.querySelector('.message-list');\n            if (container) container.scrollTop = container.scrollHeight;\n          });\n          this.$message.success(`文档 \"${response.data.filename}\" 上传成功！`);\n        } else {\n          throw new Error('文档上传失败');\n        }\n      } catch (error) {\n        console.error('文档上传失败:', error);\n        const errorMsg = {\n          text: `❌ 文档上传失败: ${error.response?.data?.error || error.message}`,\n          isUser: false,\n          timestamp: new Date(),\n          isSystem: true\n        };\n        this.messages.push(errorMsg);\n        this.$message.error(`文档上传失败: ${error.response?.data?.error || error.message}`);\n        this.selectedFile = null;\n        this.selectedFileName = '';\n        this.currentDocumentId = null;\n      } finally {\n        this.isLoading = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["axios", "Marked", "<PERSON><PERSON><PERSON><PERSON>", "hljs", "javascript", "python", "java", "xml", "json", "css", "markdown", "bash", "registerLanguage", "data", "url", "HOST", "inputMessage", "messages", "form", "message", "timestamp", "isWaitingForAI", "currentSessionId", "pollingTimer", "selectedFile", "selected<PERSON><PERSON><PERSON><PERSON>", "currentDocumentId", "isLoading", "username", "userInitial", "created", "userInfo", "localStorage", "getItem", "parsedUserInfo", "JSON", "parse", "$store", "dispatch", "history", "getters", "getChatHistory", "Array", "isArray", "length", "map", "msg", "Date", "user", "getUserInfo", "char<PERSON>t", "toUpperCase", "mounted", "$nextTick", "container", "document", "querySelector", "scrollTop", "scrollHeight", "<PERSON><PERSON><PERSON><PERSON>", "stopPolling", "computed", "watch", "handler", "newVal", "toISOString", "deep", "methods", "markdownToHtml", "marked", "pedantic", "gfm", "breaks", "smartLists", "xhtml", "async", "langPrefix", "emptyLangClass", "highlight", "code", "lang", "getLanguage", "language", "value", "highlightAuto", "sendMessage", "trim", "userMsg", "text", "isUser", "push", "post", "then", "response", "console", "log", "session_id", "startPolling", "$message", "error", "catch", "clearInterval", "setInterval", "checkMessageStatus", "get", "is_ready", "aiMsg", "ai_response", "formatTime", "date", "getHours", "String", "getMinutes", "padStart", "gotoSummarize", "$router", "path", "gotoTest", "gotoPrj", "triggerFileInput", "$refs", "fileInput", "click", "handleFileChange", "event", "file", "target", "files", "name", "uploadDocument", "formData", "FormData", "append", "headers", "document_id", "systemMsg", "filename", "isSystem", "success", "Error", "errorMsg"], "sources": ["src/views/mainPage/chatPage.vue"], "sourcesContent": ["<template>\r\n    <el-container style=\"height: 100vh; background: linear-gradient(135deg, #f5f1e8 0%, #f0e6d2 100%)\">\r\n    <el-aside width=\"240px\" style=\"background: linear-gradient(to bottom, #e8dfc8, #d8cfb8); border-right: 1px solid #d4c9a8; border-radius: 0 12px 12px 0; box-shadow: 2px 0 10px rgba(0,0,0,0.1);overflow-x: hidden\">\r\n        <el-row :gutter=\"20\">\r\n            <div style=\"color: #5a4a3a; padding: 15px; font-size: 18px; font-weight: bold; display: flex; flex-direction: column; align-items: center;\">\r\n                <div>\r\n                    <i class=\"el-icon-connection\" style=\"margin-right: 8px; color: #8b7355\"></i>\r\n                    <span>问泉-Inquiry Spring</span>\r\n                </div>\r\n                <div style=\"margin-top: 20px;\">{{ this.$store.getters.getSelectedPrjName}}</div>\r\n            </div>   \r\n        </el-row>\r\n        <el-menu \r\n            background-color=\"#e8dfc8\"\r\n            text-color=\"#5a4a3a\"\r\n            active-text-color=\"#ffffff\"\r\n            :default-active=\"'1'\">\r\n            <el-menu-item index=\"1\" style=\"border-radius: 8px; margin: 0 8px; width: calc(100% - 16px); background: linear-gradient(135deg, #5a4a3a 0%, #3a2e24 100%); color: white; box-shadow: 0 2px 8px rgba(90, 74, 58, 0.3)\">\r\n                <i class=\"el-icon-chat-dot-round\" style=\"color: white\"></i>\r\n                <span>智能答疑</span>\r\n            </el-menu-item>\r\n            <el-menu-item @click=\"gotoSummarize\" index=\"2\" style=\"border-radius: 8px; margin: 0 8px; width: calc(100% - 16px)\">\r\n                <i class=\"el-icon-notebook-2\"></i>\r\n                <span>智慧总结</span>\r\n            </el-menu-item>\r\n            <el-menu-item @click=\"gotoTest\" index=\"3\" style=\"border-radius: 8px; margin: 0 8px; width: calc(100% - 16px)\">\r\n                <i class=\"el-icon-edit\"></i>\r\n                <span>生成小测</span>\r\n            </el-menu-item>\r\n            <el-menu-item @click=\"gotoPrj\" style=\"border-radius: 8px; margin: 8px; width: calc(100% - 16px); transition: all 0.3s\">\r\n                <i class=\"el-icon-folder-add\" style=\"color: #8b7355\"></i>\r\n                <span>管理学习项目</span>\r\n            </el-menu-item>\r\n        </el-menu>\r\n        <!-- 用户信息展示 -->\r\n        <div class=\"user-info\" style=\"position: fixed; bottom: 0; left: 0; width: 240px; padding: 15px; border-top: 1px solid #e0d6c2; background: #f1e9dd;\">\r\n            <div style=\"display: flex; align-items: center; padding: 10px;\">\r\n            <el-avatar :size=\"40\" style=\"background: #8b7355; margin-right: 10px;\">\r\n                {{ userInitial }}\r\n            </el-avatar>\r\n            <div>\r\n                <div style=\"color: #5a4a3a; font-weight: bold; font-size: 14px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 150px;\">{{ username }}</div>\r\n                <div style=\"color: #8b7355; font-size: 12px;\">已登录</div>\r\n            </div>\r\n            </div>\r\n        </div>\r\n    </el-aside>\r\n    \r\n    <el-container>\r\n        <el-main style=\"padding: 20px; display: flex; flex-direction: column; height: 100%; background-color: rgba(255,255,255,0.7); border-radius: 16px; margin: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); border: 1px solid rgba(139, 115, 85, 0.1)\">\r\n            <div class=\"chat-container\">\r\n                <div class=\"message-list\">\r\n                    <div \r\n                        v-for=\"(message, index) in messages\" \r\n                        :key=\"index\" \r\n                        :class=\"['message-bubble', message.isUser ? 'user-message' : 'ai-message']\">\r\n                        <div class=\"message-content\" v-html=\"markdownToHtml(message.text)\"></div>\r\n                        <div class=\"message-time\">{{ formatTime(message.timestamp) }}</div>\r\n                    </div>\r\n                    <!-- AI处理中提示 -->\r\n                    <div v-if=\"isWaitingForAI\" class=\"message-bubble ai-message\">\r\n                        <div class=\"message-content\">\r\n                            <span class=\"ai-loading\">\r\n                                <span class=\"dot\"></span><span class=\"dot\"></span><span class=\"dot\"></span>\r\n                            </span>\r\n                            <span style=\"margin-left: 10px;\">正在思考中...</span>\r\n                        </div>\r\n                        <div class=\"message-time\">问泉</div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"input-area\" style=\"display: flex; gap: 10px; align-items: center;\">\r\n                    <el-input \r\n                        type=\"textarea\" \r\n                        :rows=\"1\" \r\n                        placeholder=\"输入你的问题...\"\r\n                        v-model=\"inputMessage\"\r\n                        @keyup.enter.native=\"sendMessage\"\r\n                        resize=\"none\"\r\n                        style=\"flex: 1; border-radius: 24px; padding: 12px 20px;\">\r\n                    </el-input>\r\n                    <el-button\r\n                        type=\"default\"\r\n                        style=\"background: linear-gradient(135deg, #f5f1e8 0%, #e8dfc8 100%); border: none; border-radius: 24px; padding: 12px 24px; font-weight: 500; letter-spacing: 1px; box-shadow: 0 2px 6px rgba(139, 115, 85, 0.15); transition: all 0.3s ease; height: 48px; color: #8b7355; min-width: 90px; font-size: 15px; margin-left: 0; display: flex; align-items: center; justify-content: center;\"\r\n                        @click=\"triggerFileInput\"\r\n                    >\r\n                        <i class=\"el-icon-folder-add\" style=\"font-size: 26px; vertical-align: middle; display: flex; align-items: center; justify-content: center; width: 100%;\"></i>\r\n                    </el-button>\r\n                    <input\r\n                        ref=\"fileInput\"\r\n                        type=\"file\"\r\n                        style=\"display: none;\"\r\n                        @change=\"handleFileChange\"\r\n                    />\r\n                    <span v-if=\"selectedFileName\" style=\"color: #8b7355; font-size: 14px; margin-left: 4px; margin-right: 8px; max-width: 120px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: inline-block;\">{{ selectedFileName }}</span>\r\n                    <el-button \r\n                        type=\"primary\" \r\n                        style=\"background: linear-gradient(135deg, #8b7355 0%, #a0866b 100%);\r\n                               border: none;\r\n                               border-radius: 24px;\r\n                               padding: 12px 24px;\r\n                               font-weight: 500;\r\n                               letter-spacing: 1px;\r\n                               box-shadow: 0 2px 6px rgba(139, 115, 85, 0.3);\r\n                               transition: all 0.3s ease;\r\n                               height: 48px;\"\r\n                        @click=\"sendMessage\"\r\n                        :disabled=\"!inputMessage.trim()\"\r\n                        class=\"send-button\">\r\n                        <i class=\"el-icon-s-promotion\" style=\"margin-right: 6px\"></i>\r\n                        发送\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n        </el-main>\r\n    </el-container>\r\n    </el-container>\r\n</template>\r\n\r\n<style>\r\n    .el-header {\r\n        background-color: #B3C0D1;\r\n        color: #333;\r\n        line-height: 60px;\r\n    }\r\n    \r\n    .el-aside {\r\n        color: #333;\r\n    }\r\n    \r\n    .el-menu-item {\r\n        transition: all 0.3s ease;\r\n    }\r\n    \r\n    .el-menu-item:hover {\r\n        background-color: #d4c9a8;\r\n    }\r\n    \r\n    .el-menu-item.is-active {\r\n        background: linear-gradient(135deg, #a0866b 0%, #d4b999 100%) !important;\r\n        color: white !important;\r\n        box-shadow: 0 2px 8px rgba(139, 115, 85, 0.3) !important;\r\n        transform: translateY(-1px);\r\n    }\r\n    \r\n    .el-menu-item.is-active i {\r\n        color: white !important;\r\n    }\r\n    \r\n    .chat-container {\r\n        display: flex;\r\n        flex-direction: column;\r\n        height: 100%;\r\n        padding: 25px;\r\n        background-color: rgba(255,255,255,0.7);\r\n        border-radius: 16px;\r\n        box-shadow: 0 4px 20px rgba(0,0,0,0.08);\r\n        border: 1px solid rgba(139, 115, 85, 0.1);\r\n    }\r\n    \r\n    .message-list {\r\n        flex: 1;\r\n        overflow-y: auto;\r\n        margin-bottom: 20px;\r\n        padding: 20px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        gap: 18px;\r\n        background-color: rgba(255,255,255,0.5);\r\n        border-radius: 12px;\r\n        scrollbar-width: thin;\r\n        scrollbar-color: #8b7355 #f0e6d2;\r\n    }\r\n    \r\n    .message-list::-webkit-scrollbar {\r\n        width: 6px;\r\n    }\r\n    \r\n    .message-list::-webkit-scrollbar-thumb {\r\n        background-color: #8b7355;\r\n        border-radius: 3px;\r\n    }\r\n    \r\n    .message-list::-webkit-scrollbar-track {\r\n        background-color: #f0e6d2;\r\n    }\r\n    \r\n    .message-bubble {\r\n        max-width: 80%;\r\n        padding: 22px 28px; /* 增大留白 */\r\n        border-radius: 14px;\r\n        position: relative;\r\n        word-break: break-word;\r\n        box-shadow: 0 2px 6px rgba(0,0,0,0.1);\r\n        font-family: 'Georgia', serif;\r\n        transition: all 0.3s ease;\r\n    }\r\n    \r\n    .message-bubble:hover {\r\n        transform: translateY(-2px);\r\n        box-shadow: 0 4px 8px rgba(0,0,0,0.15);\r\n    }\r\n    \r\n    .user-message {\r\n        align-self: flex-end;\r\n        background: linear-gradient(135deg, #e8d5c0 0%, #f5e1c8 100%);\r\n        color: #5a4a3a;\r\n        margin-left: 20%;\r\n    }\r\n    \r\n    .ai-message {\r\n        align-self: flex-start;\r\n        background: linear-gradient(135deg, #e8dfc8 0%, #f5f1e8 100%);\r\n        color: #5a4a3a;\r\n        margin-right: 20%;\r\n        border: 1px solid rgba(139, 115, 85, 0.2);\r\n    }\r\n    \r\n    .message-content {\r\n        margin-bottom: 5px;\r\n        line-height: 1.6;\r\n        font-size: 15px;\r\n    }\r\n    \r\n    .message-time {\r\n        font-size: 12px;\r\n        color: #8b7355;\r\n        text-align: right;\r\n    }\r\n    \r\n    .input-area {\r\n        margin-bottom: 20px;\r\n    }\r\n    \r\n    .el-textarea__inner {\r\n        background-color: #fffdf9;\r\n        border-color: #d4c9a8;\r\n        color: #5a4a3a;\r\n        font-family: 'Georgia', serif;\r\n        border-radius: 24px !important;\r\n        padding: 12px 20px !important;\r\n        line-height: 1.6;\r\n        min-height: 48px !important;\r\n    }\r\n\r\n    /* 发送按钮悬浮效果 */\r\n    .send-button:not(:disabled):hover {\r\n        transform: scale(1.05);\r\n        box-shadow: 0 4px 12px rgba(139, 115, 85, 0.4);\r\n        background: linear-gradient(135deg, #9d8266 0%, #b4967a 100%);\r\n    }\r\n    \r\n    .send-button:not(:disabled):active {\r\n        transform: scale(0.98);\r\n    }\r\n\r\n    /* 删除了打字动画相关的CSS */\r\n\r\n    .ai-loading {\r\n      display: inline-block;\r\n      min-width: 36px;\r\n      height: 22px;\r\n      vertical-align: middle;\r\n    }\r\n    .ai-loading .dot {\r\n      display: inline-block;\r\n      width: 8px;\r\n      height: 8px;\r\n      margin: 0 2px;\r\n      background: #8b7355;\r\n      border-radius: 50%;\r\n      animation: ai-bounce 1.2s infinite both;\r\n    }\r\n    .ai-loading .dot:nth-child(2) {\r\n      animation-delay: 0.2s;\r\n    }\r\n    .ai-loading .dot:nth-child(3) {\r\n      animation-delay: 0.4s;\r\n    }\r\n    @keyframes ai-bounce {\r\n      0%, 80%, 100% { transform: scale(0.7); opacity: 0.5; }\r\n      40% { transform: scale(1.2); opacity: 1; }\r\n    }\r\n</style>\r\n\r\n<script>\r\nimport axios from 'axios';\r\nimport { Marked } from 'marked'\r\nimport { markedHighlight } from \"marked-highlight\";\r\nimport hljs from 'highlight.js/lib/core';\r\nimport javascript from 'highlight.js/lib/languages/javascript';\r\nimport python from 'highlight.js/lib/languages/python';\r\nimport java from 'highlight.js/lib/languages/java';\r\nimport xml from 'highlight.js/lib/languages/xml';\r\nimport json from 'highlight.js/lib/languages/json';\r\nimport css from 'highlight.js/lib/languages/css';\r\nimport markdown from 'highlight.js/lib/languages/markdown';\r\nimport bash from 'highlight.js/lib/languages/bash';\r\nimport 'highlight.js/styles/github.css'; // 推荐风格，可换为其它\r\n\r\n// 注册常用语言\r\nhljs.registerLanguage('javascript', javascript);\r\nhljs.registerLanguage('python', python);\r\nhljs.registerLanguage('java', java);\r\nhljs.registerLanguage('xml', xml);\r\nhljs.registerLanguage('json', json);\r\nhljs.registerLanguage('css', css);\r\nhljs.registerLanguage('markdown', markdown);\r\nhljs.registerLanguage('bash', bash);\r\n\r\nexport default {\r\n    data() {\r\n        return {\r\n            url: this.HOST + '/chat/',\r\n            inputMessage: '',\r\n            messages: [],\r\n            form: {\r\n                message: '',\r\n                timestamp: ''\r\n            },\r\n            isWaitingForAI: false,\r\n            currentSessionId: null,\r\n            pollingTimer: null,\r\n            selectedFile: null,\r\n            selectedFileName: '', // 新增：存储选中文件名\r\n            currentDocumentId: null, // 当前文档ID\r\n\r\n\r\n            isLoading: false, // 加载状态\r\n            username: '',\r\n            userInitial: '',\r\n        }\r\n    },\r\n    created() {\r\n        // 检查localStorage中是否有用户信息\r\n        const userInfo = localStorage.getItem('userInfo');\r\n        // 将JSON字符串转换为对象\r\n        const parsedUserInfo = JSON.parse(userInfo);\r\n        // 触发Vuex action来更新store中的用户信息\r\n        this.$store.dispatch('restoreUserInfo', parsedUserInfo);\r\n\r\n        // 页面加载时从store恢复历史\r\n        const history = this.$store.getters.getChatHistory;\r\n        if (history && Array.isArray(history) && history.length > 0) {\r\n            this.messages = history.map(msg => ({...msg, timestamp: new Date(msg.timestamp)}));\r\n        }\r\n\r\n        // 获取当前用户信息\r\n        const user = this.$store.getters.getUserInfo;\r\n        if (user && user.username) {\r\n            this.username = user.username;\r\n            this.userInitial = user.username.charAt(0).toUpperCase();\r\n        } else {\r\n            this.username = '未登录';\r\n            this.userInitial = '?';\r\n        }\r\n    },\r\n    mounted() {\r\n        // 页面初次渲染后自动滚动到底部\r\n        this.$nextTick(() => {\r\n            const container = document.querySelector('.message-list');\r\n            if (container) container.scrollTop = container.scrollHeight;\r\n        });\r\n    },\r\n\r\n    beforeDestroy() {\r\n        // 组件销毁前清理轮询\r\n        this.stopPolling();\r\n    },\r\n    computed: {\r\n        // 删除了动画相关的computed属性\r\n    },\r\n    watch: {\r\n        messages: {\r\n            handler(newVal) {\r\n                // 每次对话变更都保存到store\r\n                this.$store.dispatch('updateChatHistory', newVal.map(msg => ({\r\n                    ...msg,\r\n                    timestamp: msg.timestamp instanceof Date ? msg.timestamp.toISOString() : msg.timestamp\r\n                })));\r\n            },\r\n            deep: true\r\n        }\r\n    },\r\n    methods: {\r\n        markdownToHtml(message) {\r\n            if (!message) return '';\r\n            const marked = new Marked(\r\n                markedHighlight({\r\n                    pedantic: false,\r\n                    gfm: true,\r\n                    breaks: true,\r\n                    smartLists: true,\r\n                    xhtml: true,\r\n                    async: false,\r\n                    langPrefix: 'hljs language-',\r\n                    emptyLangClass: 'no-lang',\r\n                    highlight: (code, lang) => {\r\n                        if (lang && hljs.getLanguage(lang)) {\r\n                            return hljs.highlight(code, { language: lang }).value;\r\n                        }\r\n                        return hljs.highlightAuto(code).value;\r\n                    }\r\n                })\r\n            );\r\n            return marked.parse(message);\r\n        },\r\n        sendMessage() {\r\n            if (this.inputMessage.trim() === '') return;\r\n\r\n            this.form.message = this.inputMessage;\r\n            this.form.timestamp = new Date();\r\n\r\n            // 添加用户消息\r\n            const userMsg = {\r\n                text: this.inputMessage,\r\n                isUser: true,\r\n                timestamp: this.form.timestamp\r\n            };\r\n            this.messages.push(userMsg);\r\n\r\n            // 同步到store\r\n            this.$store.dispatch('addChatMessage', {\r\n                ...userMsg,\r\n                timestamp: userMsg.timestamp.toISOString()\r\n            });\r\n\r\n            this.inputMessage = '';\r\n            this.$nextTick(() => {\r\n                const container = document.querySelector('.message-list');\r\n                if (container) container.scrollTop = container.scrollHeight;\r\n            });\r\n\r\n            // 显示等待状态\r\n            this.isWaitingForAI = true;\r\n\r\n            // 发送消息到后端API\r\n            axios.post(this.url, this.form).then((response) => {\r\n                console.log('消息发送成功:', response.data);\r\n\r\n                if (response.data.session_id) {\r\n                    this.currentSessionId = response.data.session_id;\r\n                    // 开始轮询检查状态\r\n                    this.startPolling();\r\n                } else {\r\n                    this.isWaitingForAI = false;\r\n                    this.$message.error('未获取到会话ID');\r\n                }\r\n            })\r\n            .catch(error => {\r\n                console.error('发送失败:', error);\r\n                this.$message.error('发送失败：' + error.message);\r\n                this.isWaitingForAI = false;\r\n            });\r\n        },\r\n        startPolling() {\r\n            if (this.pollingTimer) {\r\n                clearInterval(this.pollingTimer);\r\n            }\r\n\r\n            this.pollingTimer = setInterval(() => {\r\n                this.checkMessageStatus();\r\n            }, 1000); // 每秒检查一次\r\n        },\r\n\r\n        checkMessageStatus() {\r\n            if (!this.currentSessionId) return;\r\n\r\n            axios.get(`${this.HOST}/chat/status/${this.currentSessionId}/`)\r\n                .then(response => {\r\n                    console.log('状态检查:', response.data);\r\n\r\n                    if (response.data.is_ready) {\r\n                        // 消息已完成\r\n                        this.stopPolling();\r\n                        this.isWaitingForAI = false;\r\n\r\n                        // 添加AI回复到消息列表\r\n                        const aiMsg = {\r\n                            text: response.data.ai_response,\r\n                            isUser: false,\r\n                            timestamp: new Date()\r\n                        };\r\n                        this.messages.push(aiMsg);\r\n\r\n                        // 同步到store\r\n                        this.$store.dispatch('addChatMessage', {\r\n                            ...aiMsg,\r\n                            timestamp: aiMsg.timestamp.toISOString()\r\n                        });\r\n\r\n                        this.$nextTick(() => {\r\n                            const container = document.querySelector('.message-list');\r\n                            if (container) container.scrollTop = container.scrollHeight;\r\n                        });\r\n\r\n                        this.currentSessionId = null;\r\n                    }\r\n                })\r\n                .catch(error => {\r\n                    console.error('状态检查失败:', error);\r\n                    // 继续轮询，不中断\r\n                });\r\n        },\r\n\r\n        stopPolling() {\r\n            if (this.pollingTimer) {\r\n                clearInterval(this.pollingTimer);\r\n                this.pollingTimer = null;\r\n            }\r\n        },\r\n        formatTime(date) {\r\n            return `${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`;\r\n        },\r\n        gotoSummarize() {\r\n            this.$router.push({ path: '/summarize' });\r\n        },\r\n        gotoTest() {\r\n            this.$router.push({ path: '/test' });\r\n        },\r\n        gotoPrj(){\r\n            this.$router.push({ path: '/project' });\r\n        },\r\n        triggerFileInput() {\r\n            this.$refs.fileInput.click();\r\n        },\r\n        async handleFileChange(event) {\r\n            const file = event.target.files[0];\r\n            if (file) {\r\n                this.selectedFile = file;\r\n                this.selectedFileName = file.name;\r\n\r\n                // 自动上传文件\r\n                await this.uploadDocument();\r\n            } else {\r\n                this.selectedFile = null;\r\n                this.selectedFileName = '';\r\n                this.currentDocumentId = null;\r\n            }\r\n        },\r\n        async uploadDocument() {\r\n            if (!this.selectedFile) return;\r\n\r\n            const formData = new FormData();\r\n            formData.append('file', this.selectedFile);\r\n\r\n            try {\r\n                this.isLoading = true;\r\n                const response = await axios.post(this.HOST + '/chat/upload/', formData, {\r\n                    headers: {\r\n                        'Content-Type': 'multipart/form-data'\r\n                    }\r\n                });\r\n\r\n                if (response.data.document_id) {\r\n                    this.currentDocumentId = response.data.document_id;\r\n\r\n                    // 添加系统消息到聊天记录\r\n                    const systemMsg = {\r\n                        text: `📄 文档 \"${response.data.filename}\" 上传成功！现在您可以基于这个文档进行问答。`,\r\n                        isUser: false,\r\n                        timestamp: new Date(),\r\n                        isSystem: true\r\n                    };\r\n\r\n                    this.messages.push(systemMsg);\r\n\r\n                    // 同步到store\r\n                    this.$store.dispatch('addChatMessage', {\r\n                        ...systemMsg,\r\n                        timestamp: systemMsg.timestamp.toISOString()\r\n                    });\r\n\r\n                    this.$nextTick(() => {\r\n                        const container = document.querySelector('.message-list');\r\n                        if (container) container.scrollTop = container.scrollHeight;\r\n                    });\r\n\r\n                    this.$message.success(`文档 \"${response.data.filename}\" 上传成功！`);\r\n                } else {\r\n                    throw new Error('文档上传失败');\r\n                }\r\n            } catch (error) {\r\n                console.error('文档上传失败:', error);\r\n\r\n                const errorMsg = {\r\n                    text: `❌ 文档上传失败: ${error.response?.data?.error || error.message}`,\r\n                    isUser: false,\r\n                    timestamp: new Date(),\r\n                    isSystem: true\r\n                };\r\n\r\n                this.messages.push(errorMsg);\r\n\r\n                this.$message.error(`文档上传失败: ${error.response?.data?.error || error.message}`);\r\n\r\n                this.selectedFile = null;\r\n                this.selectedFileName = '';\r\n                this.currentDocumentId = null;\r\n            } finally {\r\n                this.isLoading = false;\r\n            }\r\n        }\r\n    }\r\n};\r\n</script>"], "mappings": ";;;AA6RA,OAAAA,KAAA;AACA,SAAAC,MAAA;AACA,SAAAC,eAAA;AACA,OAAAC,IAAA;AACA,OAAAC,UAAA;AACA,OAAAC,MAAA;AACA,OAAAC,IAAA;AACA,OAAAC,GAAA;AACA,OAAAC,IAAA;AACA,OAAAC,GAAA;AACA,OAAAC,QAAA;AACA,OAAAC,IAAA;AACA;;AAEA;AACAR,IAAA,CAAAS,gBAAA,eAAAR,UAAA;AACAD,IAAA,CAAAS,gBAAA,WAAAP,MAAA;AACAF,IAAA,CAAAS,gBAAA,SAAAN,IAAA;AACAH,IAAA,CAAAS,gBAAA,QAAAL,GAAA;AACAJ,IAAA,CAAAS,gBAAA,SAAAJ,IAAA;AACAL,IAAA,CAAAS,gBAAA,QAAAH,GAAA;AACAN,IAAA,CAAAS,gBAAA,aAAAF,QAAA;AACAP,IAAA,CAAAS,gBAAA,SAAAD,IAAA;AAEA;EACAE,KAAA;IACA;MACAC,GAAA,OAAAC,IAAA;MACAC,YAAA;MACAC,QAAA;MACAC,IAAA;QACAC,OAAA;QACAC,SAAA;MACA;MACAC,cAAA;MACAC,gBAAA;MACAC,YAAA;MACAC,YAAA;MACAC,gBAAA;MAAA;MACAC,iBAAA;MAAA;;MAGAC,SAAA;MAAA;MACAC,QAAA;MACAC,WAAA;IACA;EACA;EACAC,QAAA;IACA;IACA,MAAAC,QAAA,GAAAC,YAAA,CAAAC,OAAA;IACA;IACA,MAAAC,cAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAL,QAAA;IACA;IACA,KAAAM,MAAA,CAAAC,QAAA,oBAAAJ,cAAA;;IAEA;IACA,MAAAK,OAAA,QAAAF,MAAA,CAAAG,OAAA,CAAAC,cAAA;IACA,IAAAF,OAAA,IAAAG,KAAA,CAAAC,OAAA,CAAAJ,OAAA,KAAAA,OAAA,CAAAK,MAAA;MACA,KAAA3B,QAAA,GAAAsB,OAAA,CAAAM,GAAA,CAAAC,GAAA;QAAA,GAAAA,GAAA;QAAA1B,SAAA,MAAA2B,IAAA,CAAAD,GAAA,CAAA1B,SAAA;MAAA;IACA;;IAEA;IACA,MAAA4B,IAAA,QAAAX,MAAA,CAAAG,OAAA,CAAAS,WAAA;IACA,IAAAD,IAAA,IAAAA,IAAA,CAAApB,QAAA;MACA,KAAAA,QAAA,GAAAoB,IAAA,CAAApB,QAAA;MACA,KAAAC,WAAA,GAAAmB,IAAA,CAAApB,QAAA,CAAAsB,MAAA,IAAAC,WAAA;IACA;MACA,KAAAvB,QAAA;MACA,KAAAC,WAAA;IACA;EACA;EACAuB,QAAA;IACA;IACA,KAAAC,SAAA;MACA,MAAAC,SAAA,GAAAC,QAAA,CAAAC,aAAA;MACA,IAAAF,SAAA,EAAAA,SAAA,CAAAG,SAAA,GAAAH,SAAA,CAAAI,YAAA;IACA;EACA;EAEAC,cAAA;IACA;IACA,KAAAC,WAAA;EACA;EACAC,QAAA;IACA;EAAA,CACA;EACAC,KAAA;IACA7C,QAAA;MACA8C,QAAAC,MAAA;QACA;QACA,KAAA3B,MAAA,CAAAC,QAAA,sBAAA0B,MAAA,CAAAnB,GAAA,CAAAC,GAAA;UACA,GAAAA,GAAA;UACA1B,SAAA,EAAA0B,GAAA,CAAA1B,SAAA,YAAA2B,IAAA,GAAAD,GAAA,CAAA1B,SAAA,CAAA6C,WAAA,KAAAnB,GAAA,CAAA1B;QACA;MACA;MACA8C,IAAA;IACA;EACA;EACAC,OAAA;IACAC,eAAAjD,OAAA;MACA,KAAAA,OAAA;MACA,MAAAkD,MAAA,OAAApE,MAAA,CACAC,eAAA;QACAoE,QAAA;QACAC,GAAA;QACAC,MAAA;QACAC,UAAA;QACAC,KAAA;QACAC,KAAA;QACAC,UAAA;QACAC,cAAA;QACAC,SAAA,EAAAA,CAAAC,IAAA,EAAAC,IAAA;UACA,IAAAA,IAAA,IAAA7E,IAAA,CAAA8E,WAAA,CAAAD,IAAA;YACA,OAAA7E,IAAA,CAAA2E,SAAA,CAAAC,IAAA;cAAAG,QAAA,EAAAF;YAAA,GAAAG,KAAA;UACA;UACA,OAAAhF,IAAA,CAAAiF,aAAA,CAAAL,IAAA,EAAAI,KAAA;QACA;MACA,EACA;MACA,OAAAd,MAAA,CAAAjC,KAAA,CAAAjB,OAAA;IACA;IACAkE,YAAA;MACA,SAAArE,YAAA,CAAAsE,IAAA;MAEA,KAAApE,IAAA,CAAAC,OAAA,QAAAH,YAAA;MACA,KAAAE,IAAA,CAAAE,SAAA,OAAA2B,IAAA;;MAEA;MACA,MAAAwC,OAAA;QACAC,IAAA,OAAAxE,YAAA;QACAyE,MAAA;QACArE,SAAA,OAAAF,IAAA,CAAAE;MACA;MACA,KAAAH,QAAA,CAAAyE,IAAA,CAAAH,OAAA;;MAEA;MACA,KAAAlD,MAAA,CAAAC,QAAA;QACA,GAAAiD,OAAA;QACAnE,SAAA,EAAAmE,OAAA,CAAAnE,SAAA,CAAA6C,WAAA;MACA;MAEA,KAAAjD,YAAA;MACA,KAAAqC,SAAA;QACA,MAAAC,SAAA,GAAAC,QAAA,CAAAC,aAAA;QACA,IAAAF,SAAA,EAAAA,SAAA,CAAAG,SAAA,GAAAH,SAAA,CAAAI,YAAA;MACA;;MAEA;MACA,KAAArC,cAAA;;MAEA;MACArB,KAAA,CAAA2F,IAAA,MAAA7E,GAAA,OAAAI,IAAA,EAAA0E,IAAA,CAAAC,QAAA;QACAC,OAAA,CAAAC,GAAA,YAAAF,QAAA,CAAAhF,IAAA;QAEA,IAAAgF,QAAA,CAAAhF,IAAA,CAAAmF,UAAA;UACA,KAAA1E,gBAAA,GAAAuE,QAAA,CAAAhF,IAAA,CAAAmF,UAAA;UACA;UACA,KAAAC,YAAA;QACA;UACA,KAAA5E,cAAA;UACA,KAAA6E,QAAA,CAAAC,KAAA;QACA;MACA,GACAC,KAAA,CAAAD,KAAA;QACAL,OAAA,CAAAK,KAAA,UAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA,WAAAA,KAAA,CAAAhF,OAAA;QACA,KAAAE,cAAA;MACA;IACA;IACA4E,aAAA;MACA,SAAA1E,YAAA;QACA8E,aAAA,MAAA9E,YAAA;MACA;MAEA,KAAAA,YAAA,GAAA+E,WAAA;QACA,KAAAC,kBAAA;MACA;IACA;IAEAA,mBAAA;MACA,UAAAjF,gBAAA;MAEAtB,KAAA,CAAAwG,GAAA,SAAAzF,IAAA,qBAAAO,gBAAA,KACAsE,IAAA,CAAAC,QAAA;QACAC,OAAA,CAAAC,GAAA,UAAAF,QAAA,CAAAhF,IAAA;QAEA,IAAAgF,QAAA,CAAAhF,IAAA,CAAA4F,QAAA;UACA;UACA,KAAA7C,WAAA;UACA,KAAAvC,cAAA;;UAEA;UACA,MAAAqF,KAAA;YACAlB,IAAA,EAAAK,QAAA,CAAAhF,IAAA,CAAA8F,WAAA;YACAlB,MAAA;YACArE,SAAA,MAAA2B,IAAA;UACA;UACA,KAAA9B,QAAA,CAAAyE,IAAA,CAAAgB,KAAA;;UAEA;UACA,KAAArE,MAAA,CAAAC,QAAA;YACA,GAAAoE,KAAA;YACAtF,SAAA,EAAAsF,KAAA,CAAAtF,SAAA,CAAA6C,WAAA;UACA;UAEA,KAAAZ,SAAA;YACA,MAAAC,SAAA,GAAAC,QAAA,CAAAC,aAAA;YACA,IAAAF,SAAA,EAAAA,SAAA,CAAAG,SAAA,GAAAH,SAAA,CAAAI,YAAA;UACA;UAEA,KAAApC,gBAAA;QACA;MACA,GACA8E,KAAA,CAAAD,KAAA;QACAL,OAAA,CAAAK,KAAA,YAAAA,KAAA;QACA;MACA;IACA;IAEAvC,YAAA;MACA,SAAArC,YAAA;QACA8E,aAAA,MAAA9E,YAAA;QACA,KAAAA,YAAA;MACA;IACA;IACAqF,WAAAC,IAAA;MACA,UAAAA,IAAA,CAAAC,QAAA,MAAAC,MAAA,CAAAF,IAAA,CAAAG,UAAA,IAAAC,QAAA;IACA;IACAC,cAAA;MACA,KAAAC,OAAA,CAAAzB,IAAA;QAAA0B,IAAA;MAAA;IACA;IACAC,SAAA;MACA,KAAAF,OAAA,CAAAzB,IAAA;QAAA0B,IAAA;MAAA;IACA;IACAE,QAAA;MACA,KAAAH,OAAA,CAAAzB,IAAA;QAAA0B,IAAA;MAAA;IACA;IACAG,iBAAA;MACA,KAAAC,KAAA,CAAAC,SAAA,CAAAC,KAAA;IACA;IACA,MAAAC,iBAAAC,KAAA;MACA,MAAAC,IAAA,GAAAD,KAAA,CAAAE,MAAA,CAAAC,KAAA;MACA,IAAAF,IAAA;QACA,KAAArG,YAAA,GAAAqG,IAAA;QACA,KAAApG,gBAAA,GAAAoG,IAAA,CAAAG,IAAA;;QAEA;QACA,WAAAC,cAAA;MACA;QACA,KAAAzG,YAAA;QACA,KAAAC,gBAAA;QACA,KAAAC,iBAAA;MACA;IACA;IACA,MAAAuG,eAAA;MACA,UAAAzG,YAAA;MAEA,MAAA0G,QAAA,OAAAC,QAAA;MACAD,QAAA,CAAAE,MAAA,cAAA5G,YAAA;MAEA;QACA,KAAAG,SAAA;QACA,MAAAkE,QAAA,SAAA7F,KAAA,CAAA2F,IAAA,MAAA5E,IAAA,oBAAAmH,QAAA;UACAG,OAAA;YACA;UACA;QACA;QAEA,IAAAxC,QAAA,CAAAhF,IAAA,CAAAyH,WAAA;UACA,KAAA5G,iBAAA,GAAAmE,QAAA,CAAAhF,IAAA,CAAAyH,WAAA;;UAEA;UACA,MAAAC,SAAA;YACA/C,IAAA,YAAAK,QAAA,CAAAhF,IAAA,CAAA2H,QAAA;YACA/C,MAAA;YACArE,SAAA,MAAA2B,IAAA;YACA0F,QAAA;UACA;UAEA,KAAAxH,QAAA,CAAAyE,IAAA,CAAA6C,SAAA;;UAEA;UACA,KAAAlG,MAAA,CAAAC,QAAA;YACA,GAAAiG,SAAA;YACAnH,SAAA,EAAAmH,SAAA,CAAAnH,SAAA,CAAA6C,WAAA;UACA;UAEA,KAAAZ,SAAA;YACA,MAAAC,SAAA,GAAAC,QAAA,CAAAC,aAAA;YACA,IAAAF,SAAA,EAAAA,SAAA,CAAAG,SAAA,GAAAH,SAAA,CAAAI,YAAA;UACA;UAEA,KAAAwC,QAAA,CAAAwC,OAAA,QAAA7C,QAAA,CAAAhF,IAAA,CAAA2H,QAAA;QACA;UACA,UAAAG,KAAA;QACA;MACA,SAAAxC,KAAA;QACAL,OAAA,CAAAK,KAAA,YAAAA,KAAA;QAEA,MAAAyC,QAAA;UACApD,IAAA,eAAAW,KAAA,CAAAN,QAAA,EAAAhF,IAAA,EAAAsF,KAAA,IAAAA,KAAA,CAAAhF,OAAA;UACAsE,MAAA;UACArE,SAAA,MAAA2B,IAAA;UACA0F,QAAA;QACA;QAEA,KAAAxH,QAAA,CAAAyE,IAAA,CAAAkD,QAAA;QAEA,KAAA1C,QAAA,CAAAC,KAAA,YAAAA,KAAA,CAAAN,QAAA,EAAAhF,IAAA,EAAAsF,KAAA,IAAAA,KAAA,CAAAhF,OAAA;QAEA,KAAAK,YAAA;QACA,KAAAC,gBAAA;QACA,KAAAC,iBAAA;MACA;QACA,KAAAC,SAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}