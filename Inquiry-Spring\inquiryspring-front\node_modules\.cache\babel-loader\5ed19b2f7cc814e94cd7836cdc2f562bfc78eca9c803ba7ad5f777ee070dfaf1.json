{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { mapGetters } from 'vuex';\nimport axios from 'axios';\nexport default {\n  data() {\n    return {\n      url: this.HOST + '/projects/',\n      // 文档管理对话框状态\n      manageDialogVisible: false,\n      currentProject: {\n        id: null,\n        name: '',\n        description: ''\n      },\n      // 项目表单数据\n      projectForm: {\n        name: '',\n        description: '',\n        files: []\n      },\n      // 项目列表数据\n      projects: [],\n      isSubmitting: false\n    };\n  },\n  computed: {\n    ...mapGetters(['getUsername', 'isLoggedIn']),\n    username() {\n      console.log('当前store中的用户名:', this.getUsername);\n      const username = this.getUsername;\n      if (!username && this.isLoggedIn) {\n        console.warn('已登录但用户名为空');\n      }\n      return username || '未登录';\n    },\n    userInitial() {\n      const username = this.getUsername;\n      console.log('计算用户名首字母，用户名:', username);\n      return username ? username.charAt(0).toUpperCase() : '?';\n    }\n  },\n  watch: {\n    // 监听用户名变化\n    getUsername: {\n      immediate: true,\n      handler(newUsername) {\n        console.log('用户名更新:', newUsername);\n      }\n    }\n  },\n  methods: {\n    // 上传文件前的校验\n    beforeUpload(file) {\n      const isLt10M = file.size / 1024 / 1024 < 10;\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过10MB!');\n      }\n      return isLt10M;\n    },\n    // 文件上传成功处理\n    handleUploadSuccess(response, file) {\n      if (response.data && response.data.document_id) {\n        // 上传成功后将文档加入当前项目文档列表\n        if (this.currentProject && Array.isArray(this.currentProject.documents)) {\n          this.currentProject.documents.push({\n            name: response.data.filename,\n            size: file.size ? Math.round(file.size / 1024) + 'KB' : '未知',\n            uploadTime: new Date().toLocaleString(),\n            url: response.data.url\n          });\n        }\n        this.$message.success(`${file.name} 上传成功`);\n      } else {\n        this.$message.error(response?.error || `${file.name} 上传失败`);\n      }\n    },\n    // 提交项目表单\n    submitProject() {\n      this.isSubmitting = true;\n      // 构造请求数据，包含用户名\n      const payload = {\n        name: this.projectForm.name,\n        description: this.projectForm.description,\n        username: this.username // 传递当前登录用户名\n      };\n      axios.post(this.url, payload, {\n        withCredentials: true // 携带cookie/session认证\n      }).then(res => {\n        if (res.data && res.data.data.project) {\n          this.projects.unshift({\n            id: res.data.data.project.id,\n            name: res.data.data.project.name,\n            description: res.data.data.project.description,\n            createTime: res.data.data.project.createTime\n          });\n          this.$message.success('项目创建成功');\n          this.projectForm = {\n            name: '',\n            description: '',\n            files: []\n          };\n        } else {\n          this.$message.error(res.data.error || '项目创建失败');\n        }\n      }).catch(err => {\n        this.$message.error(err.response?.data?.data.error || '项目创建失败,err');\n      }).finally(() => {\n        this.isSubmitting = false;\n      });\n    },\n    // 打开项目\n    openProject(row) {\n      //将当前项目状态信息存入store\n      this.$store.dispatch('setCurrentProject', row);\n      this.$message.info(`打开项目: ${row.name}`);\n      this.$router.push({\n        path: '/chat',\n        query: {\n          id: row.id\n        }\n      });\n      // 实际应用中这里应该跳转到项目详情页\n      // this.$router.push(`/project/${project.id}`);\n    },\n    // 退出登录\n    async unlog() {\n      this.$router.push('/');\n    },\n    // 显示文档管理对话框\n    showManageDialog(project) {\n      this.currentProject = project;\n      this.$store.dispatch('setCurrentProject', project);\n      this.manageDialogVisible = true;\n    },\n    // 获取上传URL\n    getUploadUrl() {\n      return this.HOST + '/projects/' + (this.currentProject?.id || '') + '/documents/';\n    },\n    // 检查登录状态\n    async checkLoginStatus() {\n      console.log('检查登录状态:', this.isLoggedIn);\n      console.log('当前用户名:', this.username);\n      if (!this.isLoggedIn) {\n        console.log('未登录，准备跳转到登录页');\n        this.$message.warning('请先登录');\n        this.$router.push('/');\n        return;\n      }\n\n      // 如果已登录但没有用户名，尝试重新获取用户信息\n      if (this.isLoggedIn && !this.getUsername) {\n        console.log('已登录但无用户名，尝试获取用户信息');\n        try {\n          const response = await axios.get('/api/user/info/', {\n            withCredentials: true\n          });\n          console.log('获取用户信息响应:', response.data);\n          if (response.data.success) {\n            await this.$store.dispatch('updateUserInfo', {\n              username: response.data.username\n            });\n            console.log('用户信息更新成功');\n          }\n        } catch (error) {\n          console.error('获取用户信息失败:', error);\n        }\n      }\n    },\n    // 获取用户所有项目\n    fetchUserProjects() {\n      axios.get(this.url, {\n        params: {\n          username: this.username\n        },\n        withCredentials: true\n      }).then(res => {\n        if (Array.isArray(res.data.data)) {\n          this.projects = res.data.data;\n        } else {\n          this.projects = [];\n        }\n      }).catch(() => {\n        this.projects = [];\n      });\n    },\n    gotoTaskManage() {\n      this.$router.push({\n        path: '/manage'\n      });\n    },\n    deleteProject(row) {\n      this.$confirm(`确定要删除项目“${row.name}”吗？此操作不可恢复！`, '警告', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        axios.post(this.HOST + `/projects/${row.id}/deleteProject/`, {}, {\n          withCredentials: true\n        }).then(res => {\n          const data = res.data && res.data.data ? res.data.data : res.data;\n          if (data && data.success) {\n            this.$message.success('项目删除成功');\n            this.projects = this.projects.filter(p => p.id !== row.id);\n          } else {\n            this.$message.error(data.error || '删除失败');\n          }\n        }).catch(err => {\n          this.$message.error(err.response?.data?.error || '删除失败');\n        });\n      }).catch(() => {\n        this.$message.info('已取消删除');\n      });\n    },\n    deleteDocument(row) {\n      if (!this.currentProject || !this.currentProject.id) {\n        this.$message.error('未找到当前项目');\n        return;\n      }\n      this.$confirm(`确定要删除文档“${row.name}”吗？此操作不可恢复！`, '警告', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        axios.post(this.HOST + `/projects/${this.currentProject.id}/documents/deleteDocument`, {\n          filename: row.name\n        }, {\n          withCredentials: true\n        }).then(res => {\n          const data = res.data && res.data.data ? res.data.data : res.data;\n          if (data && data.success) {\n            this.$message.success('文档删除成功');\n            this.currentProject.documents = this.currentProject.documents.filter(d => d.name !== row.name);\n          } else {\n            this.$message.error(data.error || '删除失败');\n          }\n        }).catch(err => {\n          this.$message.error(err.response?.data?.error || '删除失败');\n        });\n      }).catch(() => {\n        this.$message.info('已取消删除');\n      });\n    }\n  },\n  async created() {\n    // 检查localStorage中是否有用户信息\n    const userInfo = localStorage.getItem('userInfo');\n    // 将JSON字符串转换为对象\n    const parsedUserInfo = JSON.parse(userInfo);\n    // 触发Vuex action来更新store中的用户信息\n    this.$store.dispatch('restoreUserInfo', parsedUserInfo);\n    console.log('组件创建，当前登录状态:', this.isLoggedIn);\n    console.log('组件创建，当前用户名:', this.getUsername);\n\n    // 组件创建时检查登录状态\n    await this.$nextTick();\n    await this.checkLoginStatus();\n    this.fetchUserProjects(); // 页面加载后调用\n  }\n};", "map": {"version": 3, "names": ["mapGetters", "axios", "data", "url", "HOST", "manageDialogVisible", "currentProject", "id", "name", "description", "projectForm", "files", "projects", "isSubmitting", "computed", "username", "console", "log", "getUsername", "isLoggedIn", "warn", "userInitial", "char<PERSON>t", "toUpperCase", "watch", "immediate", "handler", "newUsername", "methods", "beforeUpload", "file", "isLt10M", "size", "$message", "error", "handleUploadSuccess", "response", "document_id", "Array", "isArray", "documents", "push", "filename", "Math", "round", "uploadTime", "Date", "toLocaleString", "success", "submitProject", "payload", "post", "withCredentials", "then", "res", "project", "unshift", "createTime", "catch", "err", "finally", "openProject", "row", "$store", "dispatch", "info", "$router", "path", "query", "unlog", "showManageDialog", "getUploadUrl", "checkLoginStatus", "warning", "get", "fetchUserProjects", "params", "gotoTaskManage", "deleteProject", "$confirm", "confirmButtonText", "cancelButtonText", "type", "filter", "p", "deleteDocument", "d", "created", "userInfo", "localStorage", "getItem", "parsedUserInfo", "JSON", "parse", "$nextTick"], "sources": ["src/views/guidePage/createProject.vue"], "sourcesContent": ["<template>\r\n  <!-- 主容器，使用与chatPage一致的背景色 -->\r\n  <el-container style=\"height: 100vh; background: linear-gradient(135deg, #f5f1e8 0%, #f0e6d2 100%)\">\r\n    <!-- 侧边栏导航 -->\r\n    <el-aside width=\"240px\" style=\"background: #f1e9dd; border-right: 1px solid #e0d6c2; border-radius: 0 12px 12px 0; box-shadow: 2px 0 10px rgba(0,0,0,0.05);overflow-x: hidden\">\r\n      <el-row :gutter=\"20\">\r\n        <div style=\"color: #5a4a3a; padding: 15px; font-size: 18px; font-weight: bold; display: flex; align-items: center;\">\r\n          <i class=\"el-icon-connection\" style=\"margin-right: 8px; color: #8b7355\"></i>\r\n          <span>问泉-Inquiry Spring</span>\r\n        </div>   \r\n      </el-row>\r\n      <el-menu \r\n        background-color=\"#f1e9dd\"\r\n        text-color=\"#5a4a3a\"\r\n        active-text-color=\"#ffffff\"\r\n        :default-active=\"'1'\">\r\n        <el-menu-item index=\"1\" style=\"border-radius: 8px; margin: 8px; width: calc(100% - 16px); background: #8b7355; color: white; box-shadow: 0 2px 8px rgba(139, 115, 85, 0.2)\">\r\n          <i class=\"el-icon-folder-add\" style=\"color: white\"></i>\r\n          <span>管理学习项目</span>\r\n        </el-menu-item>\r\n        <el-menu-item @click=\"unlog\" index=\"2\" style=\"border-radius: 8px; margin: 8px; width: calc(100% - 16px); transition: all 0.3s\">\r\n          <i class=\"el-icon-right\" style=\"color: #8b7355\"></i>\r\n          <span>退出</span>\r\n        </el-menu-item>\r\n      </el-menu>\r\n\r\n     <!-- 学习计划卡片 -->\r\n      <div \r\n          @click=\"gotoTaskManage\" \r\n          class=\"study-plan-card\"\r\n      >\r\n          <i class=\"el-icon-date\" style=\"color: #d48806\"></i>\r\n          <span>我的学习计划</span>\r\n      </div>\r\n\r\n      <!-- 用户信息展示 -->\r\n      <div class=\"user-info\" style=\"position: fixed; bottom: 0; left: 0; width: 240px; padding: 15px; border-top: 1px solid #e0d6c2; background: #f1e9dd;\">\r\n        <div style=\"display: flex; align-items: center; padding: 10px;\">\r\n          <el-avatar :size=\"40\" style=\"background: #8b7355; margin-right: 10px;\">\r\n            {{ userInitial }}\r\n          </el-avatar>\r\n          <div>\r\n            <div style=\"color: #5a4a3a; font-weight: bold; font-size: 14px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 150px;\">{{ username }}</div>\r\n            <div style=\"color: #8b7355; font-size: 12px;\">已登录</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-aside>\r\n\r\n    <!-- 主内容区 -->\r\n    <el-container>\r\n      <el-main style=\"padding: 20px;\">\r\n        <!-- 项目创建表单 -->\r\n        <div class=\"project-form\" style=\"background: white; padding: 30px; border-radius: 12px; box-shadow: 0 2px 12px rgba(0,0,0,0.05); margin-bottom: 20px;\">\r\n          <h2 style=\"color: #5a4a3a; margin-bottom: 20px;\">创建新学习项目</h2>\r\n          \r\n          <el-form ref=\"projectForm\" :model=\"projectForm\" label-width=\"100px\">\r\n            <!-- 项目名称输入 -->\r\n            <el-form-item label=\"项目名称\" prop=\"name\">\r\n              <el-input \r\n                v-model=\"projectForm.name\" \r\n                placeholder=\"输入项目名称\"\r\n                style=\"width: 300px;\"\r\n                clearable>\r\n              </el-input>\r\n            </el-form-item>\r\n\r\n            <!-- 项目描述输入 -->\r\n            <el-form-item label=\"项目描述\" prop=\"description\">\r\n              <el-input\r\n                type=\"textarea\"\r\n                :rows=\"2\"\r\n                v-model=\"projectForm.description\"\r\n                placeholder=\"输入项目描述\"\r\n                style=\"width: 80%;\"\r\n                maxlength=\"200\"\r\n                show-word-limit>\r\n              </el-input>\r\n            </el-form-item>\r\n\r\n            <!-- 提交按钮 -->\r\n            <el-form-item>\r\n              <el-button \r\n                type=\"primary\" \r\n                @click=\"submitProject\" \r\n                style=\"background: #8b7355; border: none; padding: 12px 24px;\"\r\n                :loading=\"isSubmitting\">\r\n                创建项目\r\n              </el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n\r\n        <!-- 文档管理对话框 -->\r\n        <el-dialog \r\n          title=\"项目管理\"\r\n          :visible.sync=\"manageDialogVisible\" \r\n          width=\"70%\"\r\n          custom-class=\"project-manage-dialog\"\r\n          :close-on-click-modal=\"false\">\r\n          <div class=\"dialog-content\">\r\n            <div class=\"project-info\" style=\"background: #f9f5ee; padding: 20px; border-radius: 8px; margin-bottom: 10px; display: flex; align-items: center; gap: 20px;\">\r\n              <h3 style=\"color: #5a4a3a; margin: 0;\">{{ currentProject.name }}</h3>\r\n              <p style=\"color: #8b7355; margin: 0;\">{{ currentProject.description }}</p>\r\n            </div>\r\n            \r\n            <div class=\"upload-section\" style=\"background: #f9f5ee; padding: 20px; border-radius: 8px; margin-bottom: 10px;\">\r\n              <h3 style=\"color: #5a4a3a; margin-bottom: 15px; border-bottom: 1px solid #e0d6c2; padding-bottom: 10px;\">上传新文档</h3>\r\n              <el-upload\r\n                class=\"upload-demo\"\r\n                drag\r\n                :action=\"getUploadUrl()\"\r\n                multiple\r\n                :on-success=\"handleUploadSuccess\"\r\n                :before-upload=\"beforeUpload\"\r\n                :show-file-list=\"false\"\r\n                style=\"width: 100%;\">\r\n                <i class=\"el-icon-upload\" style=\"color: #8b7355; font-size: 48px;\"></i>\r\n                <div class=\"el-upload__text\" style=\"color: #5a4a3a;\">将文件拖到此处，或<em style=\"color: #8b7355;\">点击上传</em></div>\r\n                <div class=\"el-upload__tip\" slot=\"tip\" style=\"color: #8b7355;\">支持word/pdf/txt格式</div>\r\n              </el-upload>\r\n            </div>\r\n            \r\n            <div class=\"documents-section\" style=\"background: #f9f5ee; padding: 20px; border-radius: 8px;\">\r\n              <h3 style=\"color: #5a4a3a; margin-bottom: 15px; border-bottom: 1px solid #e0d6c2; padding-bottom: 10px;\">当前项目文档</h3>\r\n              <el-table\r\n                :data=\"currentProject ? currentProject.documents : []\"\r\n                style=\"width: 100%\"\r\n                empty-text=\"暂无文档\">\r\n                <el-table-column\r\n                  prop=\"name\"\r\n                  label=\"文档名称\"\r\n                  width=\"580\">\r\n                </el-table-column>\r\n                <el-table-column\r\n                  prop=\"size\"\r\n                  label=\"大小\"\r\n                  width=\"120\">\r\n                </el-table-column>\r\n                <el-table-column\r\n                  prop=\"uploadTime\"\r\n                  label=\"上传时间\"\r\n                  width=\"180\">\r\n                </el-table-column>\r\n                <el-table-column\r\n                  label=\"操作\"\r\n                  width=\"120\">\r\n                  <template #default=\"scope\">\r\n                    <el-button \r\n                      @click=\"deleteDocument(scope.row)\" \r\n                      type=\"text\" \r\n                      style=\"color: #f56c6c;\">\r\n                      删除\r\n                    </el-button>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </div>\r\n          </div>\r\n          \r\n          <span slot=\"footer\" class=\"dialog-footer\">\r\n            <el-button @click=\"manageDialogVisible = false\" style=\"color: #5a4a3a;\">取消</el-button>\r\n            <el-button type=\"primary\" @click=\"manageDialogVisible = false\" style=\"background: #8b7355; border: none;\">确定</el-button>\r\n          </span>\r\n        </el-dialog>\r\n        \r\n        <!-- 项目列表展示 -->\r\n        <div class=\"project-list\" style=\"background: white; padding: 30px; border-radius: 12px; box-shadow: 0 2px 12px rgba(0,0,0,0.05);\">\r\n          <h2 style=\"color: #5a4a3a; margin-bottom: 20px;\">我的学习项目</h2>\r\n          \r\n          <el-table\r\n            :data=\"projects\"\r\n            style=\"width: 100%\"\r\n            empty-text=\"暂无项目，请先创建一个项目\">\r\n            <el-table-column\r\n              prop=\"name\"\r\n              label=\"项目名称\"\r\n              width=\"280\">\r\n            </el-table-column>\r\n            <el-table-column\r\n              prop=\"description\"\r\n              label=\"项目描述\">\r\n            </el-table-column>\r\n            <el-table-column\r\n              prop=\"createTime\"\r\n              label=\"创建时间\"\r\n              width=\"180\">\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"操作\"\r\n              width=\"280\">\r\n              <template #default=\"scope\">\r\n                <el-button \r\n                  @click=\"openProject(scope.row)\" \r\n                  type=\"text\" \r\n                  style=\"color: #8b7355;\">\r\n                  开始学习\r\n                </el-button>\r\n                <el-button \r\n                  @click=\"showManageDialog(scope.row)\" \r\n                  type=\"text\" \r\n                  style=\"color: #8b7355;\">\r\n                  管理文档\r\n                </el-button>\r\n                <el-button \r\n                  @click=\"deleteProject(scope.row)\" \r\n                  type=\"text\" \r\n                  style=\"color: #f56c6c; font-weight: bold;\">\r\n                  删除\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </el-main>\r\n    </el-container>\r\n  </el-container>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      url:this.HOST+'/projects/',\r\n      // 文档管理对话框状态\r\n      manageDialogVisible: false,\r\n      currentProject: {\r\n        id: null,\r\n        name: '',\r\n        description: ''\r\n      },\r\n      // 项目表单数据\r\n      projectForm: {\r\n        name: '',\r\n        description: '',\r\n        files: []\r\n      },\r\n      // 项目列表数据\r\n      projects: [],\r\n      isSubmitting: false\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters(['getUsername', 'isLoggedIn']),\r\n    username() {\r\n      console.log('当前store中的用户名:', this.getUsername);\r\n      const username = this.getUsername;\r\n      if (!username && this.isLoggedIn) {\r\n        console.warn('已登录但用户名为空');\r\n      }\r\n      return username || '未登录'\r\n    },\r\n    userInitial() {\r\n      const username = this.getUsername;\r\n      console.log('计算用户名首字母，用户名:', username);\r\n      return username ? username.charAt(0).toUpperCase() : '?'\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听用户名变化\r\n    getUsername: {\r\n      immediate: true,\r\n      handler(newUsername) {\r\n        console.log('用户名更新:', newUsername);\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    // 上传文件前的校验\r\n    beforeUpload(file) {\r\n      const isLt10M = file.size / 1024 / 1024 < 10;\r\n      if (!isLt10M) {\r\n        this.$message.error('上传文件大小不能超过10MB!');\r\n      }\r\n      return isLt10M;\r\n    },\r\n    // 文件上传成功处理\r\n    handleUploadSuccess(response, file) {\r\n      if (response.data && response.data.document_id) {\r\n        // 上传成功后将文档加入当前项目文档列表\r\n        if (this.currentProject && Array.isArray(this.currentProject.documents)) {\r\n          this.currentProject.documents.push({\r\n            name: response.data.filename,\r\n            size: file.size ? Math.round(file.size / 1024) + 'KB' : '未知',\r\n            uploadTime: new Date().toLocaleString(),\r\n            url: response.data.url\r\n          });\r\n        }\r\n        this.$message.success(`${file.name} 上传成功`);\r\n      } else {\r\n        this.$message.error(response?.error || `${file.name} 上传失败`);\r\n      }\r\n    },\r\n    // 提交项目表单\r\n    submitProject() {\r\n      this.isSubmitting = true;\r\n      // 构造请求数据，包含用户名\r\n      const payload = {\r\n        name: this.projectForm.name,\r\n        description: this.projectForm.description,\r\n        username: this.username // 传递当前登录用户名\r\n      };\r\n      axios.post(this.url, payload, {\r\n        withCredentials: true // 携带cookie/session认证\r\n      })\r\n        .then(res => {\r\n          if (res.data && res.data.data.project) {\r\n            this.projects.unshift({\r\n              id: res.data.data.project.id,\r\n              name: res.data.data.project.name,\r\n              description: res.data.data.project.description,\r\n              createTime: res.data.data.project.createTime\r\n            });\r\n            this.$message.success('项目创建成功');\r\n            this.projectForm = { name: '', description: '', files: [] };\r\n          } else {\r\n            this.$message.error(res.data.error || '项目创建失败');\r\n          }\r\n        })\r\n        .catch(err => {\r\n          this.$message.error(err.response?.data?.data.error || '项目创建失败,err');\r\n        })\r\n        .finally(() => {\r\n          this.isSubmitting = false;\r\n        });\r\n    },\r\n    // 打开项目\r\n    openProject(row) {\r\n      //将当前项目状态信息存入store\r\n      this.$store.dispatch('setCurrentProject', row);\r\n\r\n      this.$message.info(`打开项目: ${row.name}`);\r\n      this.$router.push({ path: '/chat', query: { id: row.id } });\r\n      // 实际应用中这里应该跳转到项目详情页\r\n      // this.$router.push(`/project/${project.id}`);\r\n    },\r\n    // 退出登录\r\n    async unlog() {\r\n      this.$router.push('/');\r\n    },\r\n    // 显示文档管理对话框\r\n    showManageDialog(project) {\r\n      this.currentProject = project;\r\n      this.$store.dispatch('setCurrentProject', project);\r\n      this.manageDialogVisible = true;\r\n    },\r\n    \r\n    // 获取上传URL\r\n    getUploadUrl() {\r\n      return this.HOST + '/projects/' + (this.currentProject?.id || '') + '/documents/';\r\n    },\r\n    \r\n    // 检查登录状态\r\n    async checkLoginStatus() {\r\n      console.log('检查登录状态:', this.isLoggedIn);\r\n      console.log('当前用户名:', this.username);\r\n      \r\n      if (!this.isLoggedIn) {\r\n        console.log('未登录，准备跳转到登录页');\r\n        this.$message.warning('请先登录');\r\n        this.$router.push('/');\r\n        return;\r\n      }\r\n      \r\n      // 如果已登录但没有用户名，尝试重新获取用户信息\r\n      if (this.isLoggedIn && !this.getUsername) {\r\n        console.log('已登录但无用户名，尝试获取用户信息');\r\n        try {\r\n          const response = await axios.get('/api/user/info/', {\r\n            withCredentials: true\r\n          });\r\n          console.log('获取用户信息响应:', response.data);\r\n          \r\n          if (response.data.success) {\r\n            await this.$store.dispatch('updateUserInfo', {\r\n              username: response.data.username\r\n            });\r\n            console.log('用户信息更新成功');\r\n          }\r\n        } catch (error) {\r\n          console.error('获取用户信息失败:', error);\r\n        }\r\n      }\r\n    },\r\n    // 获取用户所有项目\r\n    fetchUserProjects() {\r\n      axios.get(this.url, {\r\n        params: { username: this.username },\r\n        withCredentials: true\r\n      })\r\n        .then(res => {\r\n          if (Array.isArray(res.data.data)) {\r\n            this.projects = res.data.data;\r\n          } else {\r\n            this.projects = [];\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.projects = [];\r\n        });\r\n    },\r\n\r\n    gotoTaskManage() {\r\n      this.$router.push({ path: '/manage' });\r\n    },\r\n\r\n    deleteProject(row) {\r\n      this.$confirm(`确定要删除项目“${row.name}”吗？此操作不可恢复！`, '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        axios.post(this.HOST + `/projects/${row.id}/deleteProject/`, {}, {\r\n          withCredentials: true\r\n        }).then(res => {\r\n          const data = res.data && res.data.data ? res.data.data : res.data;\r\n          if (data && data.success) {\r\n            this.$message.success('项目删除成功');\r\n            this.projects = this.projects.filter(p => p.id !== row.id);\r\n          } else {\r\n            this.$message.error(data.error || '删除失败');\r\n          }\r\n        }).catch(err => {\r\n          this.$message.error(err.response?.data?.error || '删除失败');\r\n        });\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除');\r\n      });\r\n    },\r\n\r\n    deleteDocument(row) {\r\n      if (!this.currentProject || !this.currentProject.id) {\r\n        this.$message.error('未找到当前项目');\r\n        return;\r\n      }\r\n      this.$confirm(`确定要删除文档“${row.name}”吗？此操作不可恢复！`, '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        axios.post(this.HOST + `/projects/${this.currentProject.id}/documents/deleteDocument`, {\r\n          filename: row.name\r\n        }, {\r\n          withCredentials: true\r\n        }).then(res => {\r\n          const data = res.data && res.data.data ? res.data.data : res.data;\r\n          if (data && data.success) {\r\n            this.$message.success('文档删除成功');\r\n            this.currentProject.documents = this.currentProject.documents.filter(d => d.name !== row.name);\r\n          } else {\r\n            this.$message.error(data.error || '删除失败');\r\n          }\r\n        }).catch(err => {\r\n          this.$message.error(err.response?.data?.error || '删除失败');\r\n        });\r\n      }).catch(() => {\r\n        this.$message.info('已取消删除');\r\n      });\r\n    },\r\n  },\r\n  \r\n  async created() {\r\n    // 检查localStorage中是否有用户信息\r\n    const userInfo = localStorage.getItem('userInfo');\r\n    // 将JSON字符串转换为对象\r\n    const parsedUserInfo = JSON.parse(userInfo);\r\n    // 触发Vuex action来更新store中的用户信息\r\n    this.$store.dispatch('restoreUserInfo', parsedUserInfo);\r\n\r\n    console.log('组件创建，当前登录状态:', this.isLoggedIn);\r\n    console.log('组件创建，当前用户名:', this.getUsername);\r\n    \r\n    // 组件创建时检查登录状态\r\n    await this.$nextTick();\r\n    await this.checkLoginStatus();\r\n    this.fetchUserProjects(); // 页面加载后调用\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.project-manage-dialog {\r\n  border-radius: 12px !important;\r\n}\r\n\r\n.dialog-content {\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n  padding: 10px;\r\n}\r\n/* 使用与chatPage一致的样式 */\r\n.el-menu-item {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.el-menu-item:hover {\r\n  background-color: #d4c9a8;\r\n}\r\n\r\n.el-menu-item.is-active {\r\n  background: linear-gradient(135deg, #a0866b 0%, #d4b999 100%) !important;\r\n  color: white !important;\r\n  box-shadow: 0 2px 8px rgba(139, 115, 85, 0.3) !important;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.el-menu-item.is-active i {\r\n  color: white !important;\r\n}\r\n\r\n/* 项目卡片样式 */\r\n.project-card {\r\n  margin-bottom: 20px;\r\n  border: 1px solid rgba(139, 115, 85, 0.1);\r\n  border-radius: 8px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.project-card:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 4px 12px rgba(139, 115, 85, 0.15);\r\n}\r\n\r\n/* 用户信息样式 */\r\n.user-info {\r\n  background: #f1e9dd;\r\n  box-shadow: 0 -2px 10px rgba(0,0,0,0.05);\r\n  z-index: 1000;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.user-info:hover {\r\n  background: #e9e0d2;\r\n}\r\n\r\n/* 确保侧边栏内容不被用户信息遮挡 */\r\n.el-aside {\r\n  position: relative;\r\n  padding-bottom: 80px; /* 为用户信息区域留出空间 */\r\n}\r\n\r\n/* 用户名文本溢出处理 */\r\n.el-avatar {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 主内容区域样式 */\r\n.el-main {\r\n  overflow-y: auto;\r\n  height: 100vh;\r\n}\r\n\r\n/* 设置滚动条样式 */\r\n.el-main::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.el-main::-webkit-scrollbar-thumb {\r\n  background-color: rgba(139, 115, 85, 0.2);\r\n  border-radius: 3px;\r\n}\r\n\r\n.el-main::-webkit-scrollbar-track {\r\n  background-color: transparent;\r\n}\r\n\r\n.study-plan-card {\r\n      margin: 24px 8px 0 8px;\r\n      width: calc(100% - 16px);\r\n      border-radius: 8px;\r\n      background: #fff7e6;\r\n      color: #d48806;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      cursor: pointer;\r\n      font-size: 15px;\r\n      font-weight: 500;\r\n      gap: 8px;\r\n      box-shadow: 0 2px 8px rgba(212,136,6,0.08);\r\n      padding: 12px 0;\r\n      transition: background 0.2s, transform 0.18s, box-shadow 0.18s;\r\n  }\r\n  .study-plan-card:hover {\r\n      background: #ffe7ba;\r\n      transform: scale(1.045);\r\n      box-shadow: 0 6px 18px rgba(212,136,6,0.18);\r\n  }\r\n</style>\r\n"], "mappings": ";;;AA4NA,SAAAA,UAAA;AACA,OAAAC,KAAA;AAEA;EACAC,KAAA;IACA;MACAC,GAAA,OAAAC,IAAA;MACA;MACAC,mBAAA;MACAC,cAAA;QACAC,EAAA;QACAC,IAAA;QACAC,WAAA;MACA;MACA;MACAC,WAAA;QACAF,IAAA;QACAC,WAAA;QACAE,KAAA;MACA;MACA;MACAC,QAAA;MACAC,YAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAd,UAAA;IACAe,SAAA;MACAC,OAAA,CAAAC,GAAA,uBAAAC,WAAA;MACA,MAAAH,QAAA,QAAAG,WAAA;MACA,KAAAH,QAAA,SAAAI,UAAA;QACAH,OAAA,CAAAI,IAAA;MACA;MACA,OAAAL,QAAA;IACA;IACAM,YAAA;MACA,MAAAN,QAAA,QAAAG,WAAA;MACAF,OAAA,CAAAC,GAAA,kBAAAF,QAAA;MACA,OAAAA,QAAA,GAAAA,QAAA,CAAAO,MAAA,IAAAC,WAAA;IACA;EACA;EACAC,KAAA;IACA;IACAN,WAAA;MACAO,SAAA;MACAC,QAAAC,WAAA;QACAX,OAAA,CAAAC,GAAA,WAAAU,WAAA;MACA;IACA;EACA;EACAC,OAAA;IACA;IACAC,aAAAC,IAAA;MACA,MAAAC,OAAA,GAAAD,IAAA,CAAAE,IAAA;MACA,KAAAD,OAAA;QACA,KAAAE,QAAA,CAAAC,KAAA;MACA;MACA,OAAAH,OAAA;IACA;IACA;IACAI,oBAAAC,QAAA,EAAAN,IAAA;MACA,IAAAM,QAAA,CAAAlC,IAAA,IAAAkC,QAAA,CAAAlC,IAAA,CAAAmC,WAAA;QACA;QACA,SAAA/B,cAAA,IAAAgC,KAAA,CAAAC,OAAA,MAAAjC,cAAA,CAAAkC,SAAA;UACA,KAAAlC,cAAA,CAAAkC,SAAA,CAAAC,IAAA;YACAjC,IAAA,EAAA4B,QAAA,CAAAlC,IAAA,CAAAwC,QAAA;YACAV,IAAA,EAAAF,IAAA,CAAAE,IAAA,GAAAW,IAAA,CAAAC,KAAA,CAAAd,IAAA,CAAAE,IAAA;YACAa,UAAA,MAAAC,IAAA,GAAAC,cAAA;YACA5C,GAAA,EAAAiC,QAAA,CAAAlC,IAAA,CAAAC;UACA;QACA;QACA,KAAA8B,QAAA,CAAAe,OAAA,IAAAlB,IAAA,CAAAtB,IAAA;MACA;QACA,KAAAyB,QAAA,CAAAC,KAAA,CAAAE,QAAA,EAAAF,KAAA,OAAAJ,IAAA,CAAAtB,IAAA;MACA;IACA;IACA;IACAyC,cAAA;MACA,KAAApC,YAAA;MACA;MACA,MAAAqC,OAAA;QACA1C,IAAA,OAAAE,WAAA,CAAAF,IAAA;QACAC,WAAA,OAAAC,WAAA,CAAAD,WAAA;QACAM,QAAA,OAAAA,QAAA;MACA;MACAd,KAAA,CAAAkD,IAAA,MAAAhD,GAAA,EAAA+C,OAAA;QACAE,eAAA;MACA,GACAC,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAApD,IAAA,IAAAoD,GAAA,CAAApD,IAAA,CAAAA,IAAA,CAAAqD,OAAA;UACA,KAAA3C,QAAA,CAAA4C,OAAA;YACAjD,EAAA,EAAA+C,GAAA,CAAApD,IAAA,CAAAA,IAAA,CAAAqD,OAAA,CAAAhD,EAAA;YACAC,IAAA,EAAA8C,GAAA,CAAApD,IAAA,CAAAA,IAAA,CAAAqD,OAAA,CAAA/C,IAAA;YACAC,WAAA,EAAA6C,GAAA,CAAApD,IAAA,CAAAA,IAAA,CAAAqD,OAAA,CAAA9C,WAAA;YACAgD,UAAA,EAAAH,GAAA,CAAApD,IAAA,CAAAA,IAAA,CAAAqD,OAAA,CAAAE;UACA;UACA,KAAAxB,QAAA,CAAAe,OAAA;UACA,KAAAtC,WAAA;YAAAF,IAAA;YAAAC,WAAA;YAAAE,KAAA;UAAA;QACA;UACA,KAAAsB,QAAA,CAAAC,KAAA,CAAAoB,GAAA,CAAApD,IAAA,CAAAgC,KAAA;QACA;MACA,GACAwB,KAAA,CAAAC,GAAA;QACA,KAAA1B,QAAA,CAAAC,KAAA,CAAAyB,GAAA,CAAAvB,QAAA,EAAAlC,IAAA,EAAAA,IAAA,CAAAgC,KAAA;MACA,GACA0B,OAAA;QACA,KAAA/C,YAAA;MACA;IACA;IACA;IACAgD,YAAAC,GAAA;MACA;MACA,KAAAC,MAAA,CAAAC,QAAA,sBAAAF,GAAA;MAEA,KAAA7B,QAAA,CAAAgC,IAAA,UAAAH,GAAA,CAAAtD,IAAA;MACA,KAAA0D,OAAA,CAAAzB,IAAA;QAAA0B,IAAA;QAAAC,KAAA;UAAA7D,EAAA,EAAAuD,GAAA,CAAAvD;QAAA;MAAA;MACA;MACA;IACA;IACA;IACA,MAAA8D,MAAA;MACA,KAAAH,OAAA,CAAAzB,IAAA;IACA;IACA;IACA6B,iBAAAf,OAAA;MACA,KAAAjD,cAAA,GAAAiD,OAAA;MACA,KAAAQ,MAAA,CAAAC,QAAA,sBAAAT,OAAA;MACA,KAAAlD,mBAAA;IACA;IAEA;IACAkE,aAAA;MACA,YAAAnE,IAAA,wBAAAE,cAAA,EAAAC,EAAA;IACA;IAEA;IACA,MAAAiE,iBAAA;MACAxD,OAAA,CAAAC,GAAA,iBAAAE,UAAA;MACAH,OAAA,CAAAC,GAAA,gBAAAF,QAAA;MAEA,UAAAI,UAAA;QACAH,OAAA,CAAAC,GAAA;QACA,KAAAgB,QAAA,CAAAwC,OAAA;QACA,KAAAP,OAAA,CAAAzB,IAAA;QACA;MACA;;MAEA;MACA,SAAAtB,UAAA,UAAAD,WAAA;QACAF,OAAA,CAAAC,GAAA;QACA;UACA,MAAAmB,QAAA,SAAAnC,KAAA,CAAAyE,GAAA;YACAtB,eAAA;UACA;UACApC,OAAA,CAAAC,GAAA,cAAAmB,QAAA,CAAAlC,IAAA;UAEA,IAAAkC,QAAA,CAAAlC,IAAA,CAAA8C,OAAA;YACA,WAAAe,MAAA,CAAAC,QAAA;cACAjD,QAAA,EAAAqB,QAAA,CAAAlC,IAAA,CAAAa;YACA;YACAC,OAAA,CAAAC,GAAA;UACA;QACA,SAAAiB,KAAA;UACAlB,OAAA,CAAAkB,KAAA,cAAAA,KAAA;QACA;MACA;IACA;IACA;IACAyC,kBAAA;MACA1E,KAAA,CAAAyE,GAAA,MAAAvE,GAAA;QACAyE,MAAA;UAAA7D,QAAA,OAAAA;QAAA;QACAqC,eAAA;MACA,GACAC,IAAA,CAAAC,GAAA;QACA,IAAAhB,KAAA,CAAAC,OAAA,CAAAe,GAAA,CAAApD,IAAA,CAAAA,IAAA;UACA,KAAAU,QAAA,GAAA0C,GAAA,CAAApD,IAAA,CAAAA,IAAA;QACA;UACA,KAAAU,QAAA;QACA;MACA,GACA8C,KAAA;QACA,KAAA9C,QAAA;MACA;IACA;IAEAiE,eAAA;MACA,KAAAX,OAAA,CAAAzB,IAAA;QAAA0B,IAAA;MAAA;IACA;IAEAW,cAAAhB,GAAA;MACA,KAAAiB,QAAA,YAAAjB,GAAA,CAAAtD,IAAA;QACAwE,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA7B,IAAA;QACApD,KAAA,CAAAkD,IAAA,MAAA/C,IAAA,gBAAA0D,GAAA,CAAAvD,EAAA;UACA6C,eAAA;QACA,GAAAC,IAAA,CAAAC,GAAA;UACA,MAAApD,IAAA,GAAAoD,GAAA,CAAApD,IAAA,IAAAoD,GAAA,CAAApD,IAAA,CAAAA,IAAA,GAAAoD,GAAA,CAAApD,IAAA,CAAAA,IAAA,GAAAoD,GAAA,CAAApD,IAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAA8C,OAAA;YACA,KAAAf,QAAA,CAAAe,OAAA;YACA,KAAApC,QAAA,QAAAA,QAAA,CAAAuE,MAAA,CAAAC,CAAA,IAAAA,CAAA,CAAA7E,EAAA,KAAAuD,GAAA,CAAAvD,EAAA;UACA;YACA,KAAA0B,QAAA,CAAAC,KAAA,CAAAhC,IAAA,CAAAgC,KAAA;UACA;QACA,GAAAwB,KAAA,CAAAC,GAAA;UACA,KAAA1B,QAAA,CAAAC,KAAA,CAAAyB,GAAA,CAAAvB,QAAA,EAAAlC,IAAA,EAAAgC,KAAA;QACA;MACA,GAAAwB,KAAA;QACA,KAAAzB,QAAA,CAAAgC,IAAA;MACA;IACA;IAEAoB,eAAAvB,GAAA;MACA,UAAAxD,cAAA,UAAAA,cAAA,CAAAC,EAAA;QACA,KAAA0B,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAA6C,QAAA,YAAAjB,GAAA,CAAAtD,IAAA;QACAwE,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA7B,IAAA;QACApD,KAAA,CAAAkD,IAAA,MAAA/C,IAAA,qBAAAE,cAAA,CAAAC,EAAA;UACAmC,QAAA,EAAAoB,GAAA,CAAAtD;QACA;UACA4C,eAAA;QACA,GAAAC,IAAA,CAAAC,GAAA;UACA,MAAApD,IAAA,GAAAoD,GAAA,CAAApD,IAAA,IAAAoD,GAAA,CAAApD,IAAA,CAAAA,IAAA,GAAAoD,GAAA,CAAApD,IAAA,CAAAA,IAAA,GAAAoD,GAAA,CAAApD,IAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAA8C,OAAA;YACA,KAAAf,QAAA,CAAAe,OAAA;YACA,KAAA1C,cAAA,CAAAkC,SAAA,QAAAlC,cAAA,CAAAkC,SAAA,CAAA2C,MAAA,CAAAG,CAAA,IAAAA,CAAA,CAAA9E,IAAA,KAAAsD,GAAA,CAAAtD,IAAA;UACA;YACA,KAAAyB,QAAA,CAAAC,KAAA,CAAAhC,IAAA,CAAAgC,KAAA;UACA;QACA,GAAAwB,KAAA,CAAAC,GAAA;UACA,KAAA1B,QAAA,CAAAC,KAAA,CAAAyB,GAAA,CAAAvB,QAAA,EAAAlC,IAAA,EAAAgC,KAAA;QACA;MACA,GAAAwB,KAAA;QACA,KAAAzB,QAAA,CAAAgC,IAAA;MACA;IACA;EACA;EAEA,MAAAsB,QAAA;IACA;IACA,MAAAC,QAAA,GAAAC,YAAA,CAAAC,OAAA;IACA;IACA,MAAAC,cAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAL,QAAA;IACA;IACA,KAAAzB,MAAA,CAAAC,QAAA,oBAAA2B,cAAA;IAEA3E,OAAA,CAAAC,GAAA,sBAAAE,UAAA;IACAH,OAAA,CAAAC,GAAA,qBAAAC,WAAA;;IAEA;IACA,WAAA4E,SAAA;IACA,WAAAtB,gBAAA;IACA,KAAAG,iBAAA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}