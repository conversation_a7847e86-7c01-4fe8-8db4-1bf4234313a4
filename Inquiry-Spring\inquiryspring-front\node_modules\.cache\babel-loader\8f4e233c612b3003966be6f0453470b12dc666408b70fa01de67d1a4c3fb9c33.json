{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport axios from 'axios';\nimport { Marked } from 'marked';\nimport { markedHighlight } from \"marked-highlight\";\nimport hljs from 'highlight.js/lib/core';\nexport default {\n  data() {\n    return {\n      panel: [],\n      items: 0,\n      testVisible: false,\n      testReq: {\n        num: \"\",\n        type: [],\n        level: \"\",\n        desc: \"\"\n      },\n      q: \"ddd\",\n      options: [{\n        value: 'A',\n        label: 'A'\n      }, {\n        value: 'B',\n        label: 'B'\n      }, {\n        value: 'C',\n        label: 'C'\n      }, {\n        value: 'D',\n        label: 'D'\n      }],\n      options_2: [{\n        value: '正确',\n        label: '正确'\n      }, {\n        value: '错误',\n        label: '错误'\n      }],\n      question: [{\n        type: \"单选题\",\n        question: \"1+1=?\",\n        answer: \"2\",\n        analysis: \"2是正确的答案\"\n      }, {\n        type: \"多选题\",\n        question: \"2+2=?\",\n        answer: \"4\",\n        analysis: \"4是正确的答案\"\n      }, {\n        type: \"判断题\",\n        question: \"3>2?\",\n        answer: \"是\",\n        analysis: \"3大于2\"\n      }, {\n        type: \"填空题\",\n        question: \"4-1=?\",\n        answer: \"3\",\n        analysis: \"3是正确的答案\"\n      }],\n      answer: [],\n      // 初始化答案数组\n      answerStatus: [],\n      // 答案正误状态\n      showAnalysis: [],\n      // 控制每题解析显示\n      loading: false,\n      // 是否显示加载动画\n      username: '',\n      userInitial: ''\n    };\n  },\n  created() {\n    // 检查localStorage中是否有用户信息\n    const userInfo = localStorage.getItem('userInfo');\n    // 将JSON字符串转换为对象\n    const parsedUserInfo = JSON.parse(userInfo);\n    // 触发Vuex action来更新store中的用户信息\n    this.$store.dispatch('restoreUserInfo', parsedUserInfo);\n\n    // 获取当前用户信息\n    const user = this.$store.getters.getUserInfo;\n    if (user && user.username) {\n      this.username = user.username;\n      this.userInitial = user.username.charAt(0).toUpperCase();\n    } else {\n      this.username = '未登录';\n      this.userInitial = '?';\n    }\n  },\n  methods: {\n    gotoChat() {\n      this.$router.push({\n        path: '/chat'\n      });\n    },\n    gotoSummarize() {\n      this.$router.push({\n        path: '/summarize'\n      });\n    },\n    gotoPrj() {\n      this.$router.push({\n        path: '/project'\n      });\n    },\n    all() {\n      this.panel = [...Array(this.items).keys()].map((k, i) => i);\n    },\n    // Reset the panel\n    none() {\n      this.panel = [];\n    },\n    generateTest() {\n      this.answerStatus = [],\n      // 答案正误状态\n      this.showAnalysis = [],\n      // 控制每题解析显示\n      this.answer = [],\n      // 初始化答案数组\n      this.loading = true; // 开始加载动画\n      this.items = this.testReq.num;\n      if (this.items > 0) {\n        this.testVisible = true;\n      }\n      setTimeout(() => {\n        // 模拟5秒加载\n        axios.post('/api/test/', this.testReq).then(res => {\n          this.question = res.data.AIQuestion;\n          this.showAnalysis = this.question.map(() => false); // 生成新题后重置解析显示\n          // --- 展开动画 ---\n          this.panel = [];\n          let idx = 0;\n          const total = this.question.length;\n          const expandTimer = setInterval(() => {\n            if (idx < total) {\n              this.panel.push(idx);\n              idx++;\n            } else {\n              clearInterval(expandTimer);\n            }\n          }, 120); // 每120ms展开一个\n        }).finally(() => {\n          this.loading = false; // 加载结束，隐藏动画\n        });\n      }, 15000);\n    },\n    submitAns() {\n      // window.alert(JSON.stringify(this.answer[4]))\n      this.answerStatus = [],\n      // 答案正误状态\n      this.showAnalysis = [],\n      // 控制每题解析显示\n      // 答案核对\n      this.answerStatus = this.question.map((q, i) => {\n        if (typeof this.answer[i] !== 'string') {\n          if (typeof this.answer[i] === 'object') {\n            this.answer[i] = this.answer[i].slice().sort();\n            for (let index = 0; index < this.answer[i].length; index++) {\n              if (this.answer[i][index] != q.answer[index]) return false;\n            }\n            return true;\n          } else return false;\n        }\n        return this.answer[i].trim() === String(q.answer).trim();\n      });\n    },\n    formatQuestion(q) {\n      if (!q) return '';\n      return q.replace(/\\n/g, '<br>');\n    },\n    getAnalysis() {\n      // 显示所有题目的解析\n      this.showAnalysis = this.question.map(() => true);\n    },\n    getOptionValue(option, index) {\n      // 从选项文本中提取值，如果选项以\"A. \"开头，返回\"A\"，否则返回选项本身\n      if (typeof option === 'string' && option.match(/^[A-Z]\\.\\s/)) {\n        return option.charAt(0);\n      }\n      // 对于没有字母前缀的选项，使用字母索引\n      return String.fromCharCode(65 + index); // A, B, C, D...\n    },\n    markMessage(message) {\n      if (!message) return '';\n      const marked = new Marked(markedHighlight({\n        pedantic: false,\n        gfm: true,\n        breaks: true,\n        smartLists: true,\n        xhtml: true,\n        async: false,\n        langPrefix: 'hljs language-',\n        emptyLangClass: 'no-lang',\n        highlight: code => {\n          return hljs.highlightAuto(code).value;\n        }\n      }));\n      return marked.parse(message);\n    }\n  }\n};", "map": {"version": 3, "names": ["axios", "Marked", "<PERSON><PERSON><PERSON><PERSON>", "hljs", "data", "panel", "items", "testVisible", "testReq", "num", "type", "level", "desc", "q", "options", "value", "label", "options_2", "question", "answer", "analysis", "answerStatus", "showAnalysis", "loading", "username", "userInitial", "created", "userInfo", "localStorage", "getItem", "parsedUserInfo", "JSON", "parse", "$store", "dispatch", "user", "getters", "getUserInfo", "char<PERSON>t", "toUpperCase", "methods", "gotoChat", "$router", "push", "path", "gotoSummarize", "gotoPrj", "all", "Array", "keys", "map", "k", "i", "none", "generateTest", "setTimeout", "post", "then", "res", "AIQuestion", "idx", "total", "length", "expandTimer", "setInterval", "clearInterval", "finally", "submitAns", "slice", "sort", "index", "trim", "String", "formatQuestion", "replace", "getAnalysis", "getOptionValue", "option", "match", "fromCharCode", "markMessage", "message", "marked", "pedantic", "gfm", "breaks", "smartLists", "xhtml", "async", "langPrefix", "emptyLangClass", "highlight", "code", "highlightAuto"], "sources": ["src/views/mainPage/testPage.vue"], "sourcesContent": ["<template>\r\n    <el-container style=\"height: 100vh; background: linear-gradient(135deg, #f5f1e8 0%, #f0e6d2 100%)\">\r\n    <el-aside width=\"240px\" style=\"background: linear-gradient(to bottom, #e8dfc8, #d8cfb8); border-right: 1px solid #d4c9a8; border-radius: 0 12px 12px 0; box-shadow: 2px 0 10px rgba(0,0,0,0.1);overflow-x: hidden\">\r\n        <el-row :gutter=\"20\">\r\n            <div style=\"color: #5a4a3a; padding: 15px; font-size: 18px; font-weight: bold; display: flex; flex-direction: column; align-items: center;\">\r\n                <div>\r\n                    <i class=\"el-icon-connection\" style=\"margin-right: 8px; color: #8b7355\"></i>\r\n                    <span>问泉-Inquiry Spring</span>\r\n                </div>\r\n                <div style=\"margin-top: 20px;\">{{ this.$store.getters.getSelectedPrjName}}</div>\r\n            </div>   \r\n        </el-row>\r\n        <el-menu \r\n            background-color=\"#e8dfc8\"\r\n            text-color=\"#5a4a3a\"\r\n            active-text-color=\"#ffffff\"\r\n            :default-active=\"'1'\">\r\n            <el-menu-item @click=\"gotoChat\" index=\"2\" style=\"border-radius: 8px; margin: 0 8px; width: calc(100% - 16px)\">\r\n                <i class=\"el-icon-chat-dot-round\"></i>\r\n                <span>智能答疑</span>\r\n            </el-menu-item>\r\n            <el-menu-item @click=\"gotoSummarize\" index=\"3\" style=\"border-radius: 8px; margin: 0 8px; width: calc(100% - 16px)\">\r\n                <i class=\"el-icon-chat-dot-round\"></i>\r\n                <span>智慧总结</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"1\" style=\"border-radius: 8px; margin: 0 8px; width: calc(100% - 16px); background: linear-gradient(135deg, #5a4a3a 0%, #3a2e24 100%); color: white; box-shadow: 0 2px 8px rgba(90, 74, 58, 0.3)\">\r\n                <i class=\"el-icon-edit\" style=\"color: white\"></i>\r\n                <span>生成小测</span>\r\n            </el-menu-item>\r\n            <el-menu-item @click=\"gotoPrj\" style=\"border-radius: 8px; margin: 8px; width: calc(100% - 16px); transition: all 0.3s\">\r\n                <i class=\"el-icon-folder-add\" style=\"color: #8b7355\"></i>\r\n                <span>管理学习项目</span>\r\n            </el-menu-item>\r\n        </el-menu>\r\n        <!-- 用户信息展示 -->\r\n        <div class=\"user-info\" style=\"position: fixed; bottom: 0; left: 0; width: 240px; padding: 15px; border-top: 1px solid #e0d6c2; background: #f1e9dd;\">\r\n            <div style=\"display: flex; align-items: center; padding: 10px;\">\r\n                <el-avatar :size=\"40\" style=\"background: #8b7355; margin-right: 10px;\">\r\n                    {{ userInitial }}\r\n                </el-avatar>\r\n                <div>\r\n                    <div style=\"color: #5a4a3a; font-weight: bold; font-size: 14px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 150px;\">{{ username }}</div>\r\n                    <div style=\"color: #8b7355; font-size: 12px;\">已登录</div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </el-aside>\r\n    \r\n    <el-container>\r\n        <el-main style=\"padding: 20px; display: flex; flex-direction: column; height: 100%; background-color: rgba(255,255,255,0.7); border-radius: 16px; margin: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); border: 1px solid rgba(139, 115, 85, 0.1)\">\r\n            <div class=\"content-container\" style=\"flex: 1; display: flex; flex-direction: column; gap: 30px;\">\r\n                <el-col style=\"width: 1000px; padding: 30px; background: rgba(255,255,255,0.9); border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); display: flex; gap: 10px;\">\r\n                    <!-- <div style=\"flex: 1; padding: 10px;\">\r\n                        <h3 style=\"margin-bottom: 15px; display: flex; align-items: center; gap: 5px;\">\r\n                            上传文件\r\n                            <el-tooltip content=\"将根据上传的学习材料生成测试题目\" placement=\"right\">\r\n                                <i class=\"el-icon-question\" style=\"color: #8b7355; font-size: 16px;\"></i>\r\n                            </el-tooltip>\r\n                        </h3>\r\n                        <el-upload\r\n                        class=\"upload-demo\"\r\n                        drag\r\n                        action=\"/api/test/\"\r\n                        multiple>\r\n                        <i class=\"el-icon-upload\" style=\"color: #8b7355;\"></i>\r\n                        <div class=\"el-upload__text\" style=\"color: #5a4a3a;\">将文件拖到此处，或<em style=\"color: #8b7355;\">点击上传</em></div>\r\n                        <div class=\"el-upload__tip\" slot=\"tip\" style=\"color: #8b7355;\">支持word,pdf格式</div>\r\n                        </el-upload>\r\n                    </div> -->\r\n                    <div style=\"flex: 1; padding: 15px;\">\r\n                        <h3 style=\"margin-bottom: 15px; display: flex; align-items: center; gap: 5px;\">\r\n                            测试设置\r\n                            <el-tooltip content=\"个性化生成你所需要的测试题目\" placement=\"right\">\r\n                                <i class=\"el-icon-question\" style=\"color: #8b7355; font-size: 16px;\"></i>\r\n                            </el-tooltip>\r\n                        </h3>\r\n                        <el-form ref=\"testReq\" :model=\"testReq\" label-width=\"80px\">\r\n                            <el-form-item label=\"题目数量\">\r\n                                 <div class=\"block\">\r\n                                    <el-slider\r\n                                    v-model=\"testReq.num\"\r\n                                    show-input>\r\n                                    </el-slider>\r\n                                </div>\r\n                            </el-form-item>\r\n                            <el-form-item label=\"题目类型\">\r\n                                <el-checkbox-group v-model=\"testReq.type\">\r\n                                <el-checkbox label=\"单选题\" name=\"type\"></el-checkbox>\r\n                                <el-checkbox label=\"多选题\" name=\"type\"></el-checkbox>\r\n                                <el-checkbox label=\"判断题\" name=\"type\"></el-checkbox>\r\n                                <el-checkbox label=\"填空题\" name=\"type\"></el-checkbox>\r\n                                </el-checkbox-group>\r\n                            </el-form-item>\r\n                            <el-form-item label=\"题目难度\">\r\n                                <el-radio-group v-model=\"testReq.level\">\r\n                                <el-radio label=\"简单\"></el-radio>\r\n                                <el-radio label=\"中等\"></el-radio>\r\n                                <el-radio label=\"困难\"></el-radio>\r\n                                </el-radio-group>\r\n                            </el-form-item>\r\n                            <el-form-item label=\"其他要求\">\r\n                                <el-input type=\"textarea\" v-model=\"testReq.desc\"></el-input>\r\n                            </el-form-item>\r\n                            <el-form-item>\r\n                                <v-btn @click=\"generateTest\" color=\"#8b7355\" style=\"color: white;\">\r\n                                    立即生成\r\n                                </v-btn>\r\n                            </el-form-item>\r\n                        </el-form>\r\n                    </div>\r\n                </el-col>\r\n                <el-col v-show=\"testVisible\" style=\"padding: 20px; background: rgba(255,255,255,0.9); border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);\">\r\n                    <div v-if=\"loading\" style=\"display:flex;align-items:center;justify-content:center;height:300px;\">\r\n                        <!-- 三点跳动加载动画 -->\r\n                        <div class=\"loading-dots\">\r\n                            <span></span><span></span><span></span>\r\n                        </div>\r\n                    </div>\r\n                    <div v-else>\r\n                        <!-- 题目内容区域，原有内容整体包裹到这里 -->\r\n                        <div style=\"display: flex; justify-content: center; gap: 15px; margin-bottom: 20px;\">\r\n                        </div>\r\n                        <v-expansion-panels\r\n                        v-model=\"panel\"\r\n                        multiple\r\n                        >\r\n                        <v-expansion-panel\r\n                            v-for=\"(item,i) in items\"\r\n                            :key=\"i\"\r\n                            style=\"margin-bottom: 10px; border: 1px solid rgba(139, 115, 85, 0.1); will-change: height;\"\r\n                        >\r\n                            <v-expansion-panel-header \r\n                                expand-icon=\"mdi-menu-down\" \r\n                                style=\"color: #5a4a3a; font-weight: 500;\"\r\n                                :class=\"{'v-expansion-panel-header--active': panel.includes(i)}\"\r\n                            >\r\n                                题目 {{ item }}\r\n                            </v-expansion-panel-header>\r\n                            <v-expansion-panel-content \r\n                                style=\"color: #5a4a3a; line-height: 1.6; padding: 15px;\"\r\n                                v-if=\"panel.includes(i)\"\r\n                            >\r\n                            <el-row>\r\n                                <div style=\"flex:1;\">\r\n                                    <div v-html=\"markMessage(question[i]?.type ? ('**' + question[i].type + '**') : '')\"></div>\r\n                                    <div v-html=\"markMessage(question[i]?.question)\"></div>\r\n                                </div>\r\n                            </el-row>\r\n                            <el-row>\r\n                                <span v-if=\"question[i]?.type==='单选题'\">\r\n                                    <el-select v-model=\"answer[i]\" placeholder=\"请选择\" style=\"margin-top: 15px;\">\r\n                                        <el-option\r\n                                        v-for=\"(option, index) in question[i]?.options || []\"\r\n                                        :key=\"index\"\r\n                                        :label=\"option\"\r\n                                        :value=\"getOptionValue(option, index)\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </span>\r\n                                <span v-else-if=\"question[i]?.type==='多选题'\">\r\n                                    <el-select v-model=\"answer[i]\" multiple placeholder=\"请选择\" style=\"margin-top: 15px;\">\r\n                                        <el-option\r\n                                        v-for=\"(option, index) in question[i]?.options || []\"\r\n                                        :key=\"index\"\r\n                                        :label=\"option\"\r\n                                        :value=\"getOptionValue(option, index)\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </span>\r\n                                <span v-else-if=\"question[i]?.type==='判断题'\">\r\n                                    <el-select v-model=\"answer[i]\" placeholder=\"请选择\" style=\"margin-top: 15px;\">\r\n                                        <el-option\r\n                                        v-for=\"(option, index) in question[i]?.options || ['正确', '错误']\"\r\n                                        :key=\"index\"\r\n                                        :label=\"option\"\r\n                                        :value=\"option\">\r\n                                        </el-option>\r\n                                    </el-select>\r\n                                </span>\r\n                                <span v-else-if=\"question[i]?.type==='填空题'\" style=\"margin-top: 15px;\">\r\n                                    <v-text-field \r\n                                        label=\"输入答案\" \r\n                                        v-model=\"answer[i]\"\r\n                                    ></v-text-field>\r\n                                </span>\r\n                               \r\n                                <span v-if=\"answerStatus && answerStatus[i] === true\" style=\"color:#4caf50;margin-left:10px;\">✔ 正确</span>\r\n                                <span v-else-if=\"answerStatus && answerStatus[i] === false\" style=\"color:#f44336;margin-left:10px;\">✘ 错误，正确答案：{{ question[i].answer }}</span>\r\n                            </el-row>\r\n                            <el-row v-if=\"showAnalysis && showAnalysis[i]\">\r\n                                <div style=\"margin-top: 10px; color: #8b7355;\">\r\n                                    <strong>解析:</strong>\r\n                                    <div v-html=\"markMessage(question[i].analysis)\"></div>\r\n                                </div>\r\n                            </el-row>\r\n                            </v-expansion-panel-content>\r\n                        </v-expansion-panel>\r\n                        </v-expansion-panels>\r\n                        <div style=\"display: flex; justify-content: center; gap: 15px; margin-bottom: 20px;\">\r\n                            <v-btn v-if=\"panel.length < items\" @click=\"all\" color=\"#8b7355\" style=\"color: white;\">\r\n                                全部展开\r\n                            </v-btn>\r\n                            <v-btn v-else @click=\"none\" color=\"#e8dfc8\" style=\"color: #5a4a3a;\">\r\n                                收起\r\n                            </v-btn>\r\n                            <v-btn @click=\"submitAns\" color=\"#8b7355\" style=\"color: white;\">\r\n                                提交答案\r\n                            </v-btn>\r\n                            <v-btn @click=\"getAnalysis\" color=\"#8b7355\" style=\"color: white;\">\r\n                                查看解析\r\n                            </v-btn>\r\n                        </div>\r\n                    </div>\r\n                </el-col>\r\n            </div>\r\n        </el-main>\r\n    </el-container>\r\n    </el-container>\r\n</template>\r\n\r\n<style>\r\n    .el-header {\r\n        background-color: #B3C0D1;\r\n        color: #333;\r\n        line-height: 60px;\r\n    }\r\n    \r\n    .el-aside {\r\n        color: #333;\r\n    }\r\n    \r\n    .el-menu-item {\r\n        transition: all 0.3s ease;\r\n    }\r\n    \r\n    .el-menu-item:hover {\r\n        background-color: #d4c9a8;\r\n    }\r\n    \r\n    .el-menu-item.is-active {\r\n        background: linear-gradient(135deg, #a0866b 0%, #d4b999 100%) !important;\r\n        color: white !important;\r\n        box-shadow: 0 2px 8px rgba(90, 74, 58, 0.3) !important;\r\n        transform: translateY(-1px);\r\n    }\r\n    \r\n    .el-menu-item.is-active i {\r\n        color: white !important;\r\n    }\r\n\r\n    .loading-dots {\r\n        display: flex;\r\n        gap: 8px;\r\n    }\r\n\r\n    .loading-dots span {\r\n        display: inline-block;\r\n        width: 12px;\r\n        height: 12px;\r\n        background: #8b7355;\r\n        border-radius: 50%;\r\n        animation: bounce 1.2s infinite both;\r\n    }\r\n\r\n    .loading-dots span:nth-child(2) {\r\n        animation-delay: 0.2s;\r\n    }\r\n\r\n    .loading-dots span:nth-child(3) {\r\n        animation-delay: 0.4s;\r\n    }\r\n\r\n    @keyframes bounce {\r\n        0%, 80%, 100% { transform: scale(1); }\r\n        40% { transform: scale(1.5); }\r\n    }\r\n</style>\r\n\r\n<script>\r\nimport axios from 'axios';\r\nimport { Marked } from 'marked'\r\nimport { markedHighlight } from \"marked-highlight\";\r\nimport hljs from 'highlight.js/lib/core';\r\n\r\nexport default {\r\n    data() {\r\n        return {\r\n            panel: [],\r\n            items: 0,\r\n            testVisible:false,\r\n            testReq:{\r\n                num:\"\",\r\n                type:[],\r\n                level:\"\",\r\n                desc:\"\"\r\n            },\r\n            q:\"ddd\",\r\n            options: [{\r\n                value: 'A',\r\n                label: 'A'\r\n                }, {\r\n                value: 'B',\r\n                label: 'B'\r\n                }, {\r\n                value: 'C',\r\n                label: 'C'\r\n                }, {\r\n                value: 'D',\r\n                label: 'D'\r\n                }],\r\n            options_2: [{\r\n                value: '正确',\r\n                label: '正确'\r\n                }, {\r\n                value: '错误',\r\n                label: '错误'\r\n                }],\r\n            question:[\r\n                {\r\n                    type:\"单选题\",\r\n                    question:\"1+1=?\",\r\n                    answer:\"2\",\r\n                    analysis:\"2是正确的答案\"\r\n                },\r\n                {\r\n                    type:\"多选题\",\r\n                    question:\"2+2=?\",\r\n                    answer:\"4\",\r\n                    analysis:\"4是正确的答案\"\r\n                },\r\n                {\r\n                    type:\"判断题\",\r\n                    question:\"3>2?\",\r\n                    answer:\"是\",\r\n                    analysis:\"3大于2\"\r\n                },\r\n                {\r\n                    type:\"填空题\",\r\n                    question:\"4-1=?\",\r\n                    answer:\"3\",\r\n                    analysis:\"3是正确的答案\"\r\n                }\r\n            ],\r\n            answer: [], // 初始化答案数组\r\n            answerStatus: [], // 答案正误状态\r\n            showAnalysis: [], // 控制每题解析显示\r\n            loading: false, // 是否显示加载动画\r\n            username: '',\r\n            userInitial: '',\r\n        }\r\n    },\r\n    created() {\r\n        // 检查localStorage中是否有用户信息\r\n        const userInfo = localStorage.getItem('userInfo');\r\n        // 将JSON字符串转换为对象\r\n        const parsedUserInfo = JSON.parse(userInfo);\r\n        // 触发Vuex action来更新store中的用户信息\r\n        this.$store.dispatch('restoreUserInfo', parsedUserInfo);\r\n\r\n        // 获取当前用户信息\r\n        const user = this.$store.getters.getUserInfo;\r\n        if (user && user.username) {\r\n            this.username = user.username;\r\n            this.userInitial = user.username.charAt(0).toUpperCase();\r\n        } else {\r\n            this.username = '未登录';\r\n            this.userInitial = '?';\r\n        }\r\n    },\r\n    methods: {\r\n        gotoChat() {\r\n            this.$router.push({ path: '/chat' });\r\n        },\r\n        gotoSummarize() {\r\n            this.$router.push({ path: '/summarize' });\r\n        },\r\n        gotoPrj(){\r\n            this.$router.push({ path: '/project' });\r\n        },\r\n        all () {\r\n            this.panel = [...Array(this.items).keys()].map((k, i) => i)\r\n        },\r\n        // Reset the panel\r\n        none () {\r\n            this.panel = []\r\n        },\r\n        generateTest(){\r\n            this.answerStatus=[], // 答案正误状态\r\n            this.showAnalysis=[], // 控制每题解析显示\r\n            this.answer=[], // 初始化答案数组\r\n            this.loading = true; // 开始加载动画\r\n            this.items = this.testReq.num;\r\n            if(this.items > 0){\r\n                this.testVisible = true;\r\n            }\r\n            setTimeout(() => { // 模拟5秒加载\r\n                axios.post('/api/test/', this.testReq).then(res => {\r\n                    this.question = res.data.AIQuestion;\r\n                    this.showAnalysis = this.question.map(() => false); // 生成新题后重置解析显示\r\n                    // --- 展开动画 ---\r\n                    this.panel = [];\r\n                    let idx = 0;\r\n                    const total = this.question.length;\r\n                    const expandTimer = setInterval(() => {\r\n                        if(idx < total) {\r\n                            this.panel.push(idx);\r\n                            idx++;\r\n                        } else {\r\n                            clearInterval(expandTimer);\r\n                        }\r\n                    }, 120); // 每120ms展开一个\r\n                }).finally(() => {\r\n                    this.loading = false; // 加载结束，隐藏动画\r\n                });\r\n            }, 15000);\r\n        },\r\n        submitAns() {\r\n            // window.alert(JSON.stringify(this.answer[4]))\r\n            this.answerStatus=[], // 答案正误状态\r\n            this.showAnalysis=[], // 控制每题解析显示\r\n            // 答案核对\r\n            this.answerStatus = this.question.map((q, i) => {\r\n                if (typeof this.answer[i] !== 'string'){\r\n                    if(typeof this.answer[i] === 'object'){\r\n                        this.answer[i] = this.answer[i].slice().sort();\r\n                        for(let index=0; index < this.answer[i].length; index++){\r\n                            if(this.answer[i][index]!=q.answer[index]) return false;\r\n                        }\r\n                        return true;\r\n                    }\r\n                    else return false;\r\n                }\r\n                return this.answer[i].trim() === String(q.answer).trim();\r\n            });\r\n        },\r\n        formatQuestion(q) {\r\n            if (!q) return '';\r\n            return q.replace(/\\n/g, '<br>');\r\n        },\r\n        getAnalysis() {\r\n            // 显示所有题目的解析\r\n            this.showAnalysis = this.question.map(() => true);\r\n        },\r\n        getOptionValue(option, index) {\r\n            // 从选项文本中提取值，如果选项以\"A. \"开头，返回\"A\"，否则返回选项本身\r\n            if (typeof option === 'string' && option.match(/^[A-Z]\\.\\s/)) {\r\n                return option.charAt(0);\r\n            }\r\n            // 对于没有字母前缀的选项，使用字母索引\r\n            return String.fromCharCode(65 + index); // A, B, C, D...\r\n        },\r\n        markMessage(message) {\r\n            if (!message) return '';\r\n            const marked = new Marked(\r\n                markedHighlight({\r\n                    pedantic: false,\r\n                    gfm: true,\r\n                    breaks: true,\r\n                    smartLists: true,\r\n                    xhtml: true,\r\n                    async: false,\r\n                    langPrefix: 'hljs language-',\r\n                    emptyLangClass: 'no-lang',\r\n                    highlight: (code) => {\r\n                        return hljs.highlightAuto(code).value\r\n                    }\r\n                })\r\n            );\r\n            return marked.parse(message);\r\n        },\r\n    }\r\n};\r\n</script>"], "mappings": ";;;AAuRA,OAAAA,KAAA;AACA,SAAAC,MAAA;AACA,SAAAC,eAAA;AACA,OAAAC,IAAA;AAEA;EACAC,KAAA;IACA;MACAC,KAAA;MACAC,KAAA;MACAC,WAAA;MACAC,OAAA;QACAC,GAAA;QACAC,IAAA;QACAC,KAAA;QACAC,IAAA;MACA;MACAC,CAAA;MACAC,OAAA;QACAC,KAAA;QACAC,KAAA;MACA;QACAD,KAAA;QACAC,KAAA;MACA;QACAD,KAAA;QACAC,KAAA;MACA;QACAD,KAAA;QACAC,KAAA;MACA;MACAC,SAAA;QACAF,KAAA;QACAC,KAAA;MACA;QACAD,KAAA;QACAC,KAAA;MACA;MACAE,QAAA,GACA;QACAR,IAAA;QACAQ,QAAA;QACAC,MAAA;QACAC,QAAA;MACA,GACA;QACAV,IAAA;QACAQ,QAAA;QACAC,MAAA;QACAC,QAAA;MACA,GACA;QACAV,IAAA;QACAQ,QAAA;QACAC,MAAA;QACAC,QAAA;MACA,GACA;QACAV,IAAA;QACAQ,QAAA;QACAC,MAAA;QACAC,QAAA;MACA,EACA;MACAD,MAAA;MAAA;MACAE,YAAA;MAAA;MACAC,YAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MACAC,WAAA;IACA;EACA;EACAC,QAAA;IACA;IACA,MAAAC,QAAA,GAAAC,YAAA,CAAAC,OAAA;IACA;IACA,MAAAC,cAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAL,QAAA;IACA;IACA,KAAAM,MAAA,CAAAC,QAAA,oBAAAJ,cAAA;;IAEA;IACA,MAAAK,IAAA,QAAAF,MAAA,CAAAG,OAAA,CAAAC,WAAA;IACA,IAAAF,IAAA,IAAAA,IAAA,CAAAX,QAAA;MACA,KAAAA,QAAA,GAAAW,IAAA,CAAAX,QAAA;MACA,KAAAC,WAAA,GAAAU,IAAA,CAAAX,QAAA,CAAAc,MAAA,IAAAC,WAAA;IACA;MACA,KAAAf,QAAA;MACA,KAAAC,WAAA;IACA;EACA;EACAe,OAAA;IACAC,SAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QAAAC,IAAA;MAAA;IACA;IACAC,cAAA;MACA,KAAAH,OAAA,CAAAC,IAAA;QAAAC,IAAA;MAAA;IACA;IACAE,QAAA;MACA,KAAAJ,OAAA,CAAAC,IAAA;QAAAC,IAAA;MAAA;IACA;IACAG,IAAA;MACA,KAAA1C,KAAA,OAAA2C,KAAA,MAAA1C,KAAA,EAAA2C,IAAA,IAAAC,GAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAA,CAAA;IACA;IACA;IACAC,KAAA;MACA,KAAAhD,KAAA;IACA;IACAiD,aAAA;MACA,KAAAjC,YAAA;MAAA;MACA,KAAAC,YAAA;MAAA;MACA,KAAAH,MAAA;MAAA;MACA,KAAAI,OAAA;MACA,KAAAjB,KAAA,QAAAE,OAAA,CAAAC,GAAA;MACA,SAAAH,KAAA;QACA,KAAAC,WAAA;MACA;MACAgD,UAAA;QAAA;QACAvD,KAAA,CAAAwD,IAAA,oBAAAhD,OAAA,EAAAiD,IAAA,CAAAC,GAAA;UACA,KAAAxC,QAAA,GAAAwC,GAAA,CAAAtD,IAAA,CAAAuD,UAAA;UACA,KAAArC,YAAA,QAAAJ,QAAA,CAAAgC,GAAA;UACA;UACA,KAAA7C,KAAA;UACA,IAAAuD,GAAA;UACA,MAAAC,KAAA,QAAA3C,QAAA,CAAA4C,MAAA;UACA,MAAAC,WAAA,GAAAC,WAAA;YACA,IAAAJ,GAAA,GAAAC,KAAA;cACA,KAAAxD,KAAA,CAAAsC,IAAA,CAAAiB,GAAA;cACAA,GAAA;YACA;cACAK,aAAA,CAAAF,WAAA;YACA;UACA;QACA,GAAAG,OAAA;UACA,KAAA3C,OAAA;QACA;MACA;IACA;IACA4C,UAAA;MACA;MACA,KAAA9C,YAAA;MAAA;MACA,KAAAC,YAAA;MAAA;MACA;MACA,KAAAD,YAAA,QAAAH,QAAA,CAAAgC,GAAA,EAAArC,CAAA,EAAAuC,CAAA;QACA,gBAAAjC,MAAA,CAAAiC,CAAA;UACA,gBAAAjC,MAAA,CAAAiC,CAAA;YACA,KAAAjC,MAAA,CAAAiC,CAAA,SAAAjC,MAAA,CAAAiC,CAAA,EAAAgB,KAAA,GAAAC,IAAA;YACA,SAAAC,KAAA,MAAAA,KAAA,QAAAnD,MAAA,CAAAiC,CAAA,EAAAU,MAAA,EAAAQ,KAAA;cACA,SAAAnD,MAAA,CAAAiC,CAAA,EAAAkB,KAAA,KAAAzD,CAAA,CAAAM,MAAA,CAAAmD,KAAA;YACA;YACA;UACA,OACA;QACA;QACA,YAAAnD,MAAA,CAAAiC,CAAA,EAAAmB,IAAA,OAAAC,MAAA,CAAA3D,CAAA,CAAAM,MAAA,EAAAoD,IAAA;MACA;IACA;IACAE,eAAA5D,CAAA;MACA,KAAAA,CAAA;MACA,OAAAA,CAAA,CAAA6D,OAAA;IACA;IACAC,YAAA;MACA;MACA,KAAArD,YAAA,QAAAJ,QAAA,CAAAgC,GAAA;IACA;IACA0B,eAAAC,MAAA,EAAAP,KAAA;MACA;MACA,WAAAO,MAAA,iBAAAA,MAAA,CAAAC,KAAA;QACA,OAAAD,MAAA,CAAAvC,MAAA;MACA;MACA;MACA,OAAAkC,MAAA,CAAAO,YAAA,MAAAT,KAAA;IACA;IACAU,YAAAC,OAAA;MACA,KAAAA,OAAA;MACA,MAAAC,MAAA,OAAAjF,MAAA,CACAC,eAAA;QACAiF,QAAA;QACAC,GAAA;QACAC,MAAA;QACAC,UAAA;QACAC,KAAA;QACAC,KAAA;QACAC,UAAA;QACAC,cAAA;QACAC,SAAA,EAAAC,IAAA;UACA,OAAAzF,IAAA,CAAA0F,aAAA,CAAAD,IAAA,EAAA7E,KAAA;QACA;MACA,EACA;MACA,OAAAmE,MAAA,CAAAlD,KAAA,CAAAiD,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}