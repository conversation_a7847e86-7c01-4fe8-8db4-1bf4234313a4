{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"el-container\", {\n    staticStyle: {\n      height: \"100vh\",\n      background: \"linear-gradient(135deg, #f5f1e8 0%, #f0e6d2 100%)\"\n    }\n  }, [_c(\"el-aside\", {\n    staticStyle: {\n      background: \"linear-gradient(to bottom, #e8dfc8, #d8cfb8)\",\n      \"border-right\": \"1px solid #d4c9a8\",\n      \"border-radius\": \"0 12px 12px 0\",\n      \"box-shadow\": \"2px 0 10px rgba(0,0,0,0.1)\",\n      \"overflow-x\": \"hidden\"\n    },\n    attrs: {\n      width: \"240px\"\n    }\n  }, [_c(\"el-row\", {\n    attrs: {\n      gutter: 20\n    }\n  }, [_c(\"div\", {\n    staticStyle: {\n      color: \"#5a4a3a\",\n      padding: \"15px\",\n      \"font-size\": \"18px\",\n      \"font-weight\": \"bold\",\n      display: \"flex\",\n      \"flex-direction\": \"column\",\n      \"align-items\": \"center\"\n    }\n  }, [_c(\"div\", [_c(\"i\", {\n    staticClass: \"el-icon-connection\",\n    staticStyle: {\n      \"margin-right\": \"8px\",\n      color: \"#8b7355\"\n    }\n  }), _c(\"span\", [_vm._v(\"问泉-Inquiry Spring\")])]), _c(\"div\", {\n    staticStyle: {\n      \"margin-top\": \"20px\"\n    }\n  }, [_vm._v(_vm._s(this.$store.getters.getSelectedPrjName))])])]), _c(\"el-menu\", {\n    attrs: {\n      \"background-color\": \"#e8dfc8\",\n      \"text-color\": \"#5a4a3a\",\n      \"active-text-color\": \"#ffffff\",\n      \"default-active\": \"1\"\n    }\n  }, [_c(\"el-menu-item\", {\n    staticStyle: {\n      \"border-radius\": \"8px\",\n      margin: \"0 8px\",\n      width: \"calc(100% - 16px)\",\n      background: \"linear-gradient(135deg, #5a4a3a 0%, #3a2e24 100%)\",\n      color: \"white\",\n      \"box-shadow\": \"0 2px 8px rgba(90, 74, 58, 0.3)\"\n    },\n    attrs: {\n      index: \"1\"\n    }\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-chat-dot-round\",\n    staticStyle: {\n      color: \"white\"\n    }\n  }), _c(\"span\", [_vm._v(\"智能答疑\")])]), _c(\"el-menu-item\", {\n    staticStyle: {\n      \"border-radius\": \"8px\",\n      margin: \"0 8px\",\n      width: \"calc(100% - 16px)\"\n    },\n    attrs: {\n      index: \"2\"\n    },\n    on: {\n      click: _vm.gotoSummarize\n    }\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-notebook-2\"\n  }), _c(\"span\", [_vm._v(\"智慧总结\")])]), _c(\"el-menu-item\", {\n    staticStyle: {\n      \"border-radius\": \"8px\",\n      margin: \"0 8px\",\n      width: \"calc(100% - 16px)\"\n    },\n    attrs: {\n      index: \"3\"\n    },\n    on: {\n      click: _vm.gotoTest\n    }\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-edit\"\n  }), _c(\"span\", [_vm._v(\"生成小测\")])]), _c(\"el-menu-item\", {\n    staticStyle: {\n      \"border-radius\": \"8px\",\n      margin: \"8px\",\n      width: \"calc(100% - 16px)\",\n      transition: \"all 0.3s\"\n    },\n    on: {\n      click: _vm.gotoPrj\n    }\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-folder-add\",\n    staticStyle: {\n      color: \"#8b7355\"\n    }\n  }), _c(\"span\", [_vm._v(\"管理学习项目\")])])], 1), _c(\"div\", {\n    staticClass: \"user-info\",\n    staticStyle: {\n      position: \"fixed\",\n      bottom: \"0\",\n      left: \"0\",\n      width: \"240px\",\n      padding: \"15px\",\n      \"border-top\": \"1px solid #e0d6c2\",\n      background: \"#f1e9dd\"\n    }\n  }, [_c(\"div\", {\n    staticStyle: {\n      display: \"flex\",\n      \"align-items\": \"center\",\n      padding: \"10px\"\n    }\n  }, [_c(\"el-avatar\", {\n    staticStyle: {\n      background: \"#8b7355\",\n      \"margin-right\": \"10px\"\n    },\n    attrs: {\n      size: 40\n    }\n  }, [_vm._v(\" \" + _vm._s(_vm.userInitial) + \" \")]), _c(\"div\", [_c(\"div\", {\n    staticStyle: {\n      color: \"#5a4a3a\",\n      \"font-weight\": \"bold\",\n      \"font-size\": \"14px\",\n      \"white-space\": \"nowrap\",\n      overflow: \"hidden\",\n      \"text-overflow\": \"ellipsis\",\n      \"max-width\": \"150px\"\n    }\n  }, [_vm._v(_vm._s(_vm.username))]), _c(\"div\", {\n    staticStyle: {\n      color: \"#8b7355\",\n      \"font-size\": \"12px\"\n    }\n  }, [_vm._v(\"已登录\")])])], 1)])], 1), _c(\"el-container\", [_c(\"el-main\", {\n    staticStyle: {\n      padding: \"20px\",\n      display: \"flex\",\n      \"flex-direction\": \"column\",\n      height: \"100%\",\n      \"background-color\": \"rgba(255,255,255,0.7)\",\n      \"border-radius\": \"16px\",\n      margin: \"20px\",\n      \"box-shadow\": \"0 4px 20px rgba(0,0,0,0.08)\",\n      border: \"1px solid rgba(139, 115, 85, 0.1)\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"chat-container\"\n  }, [_c(\"div\", {\n    staticClass: \"message-list\"\n  }, [_vm._l(_vm.messages, function (message, index) {\n    return _c(\"div\", {\n      key: index,\n      class: [\"message-bubble\", message.isUser ? \"user-message\" : \"ai-message\"]\n    }, [_c(\"div\", {\n      staticClass: \"message-content\",\n      domProps: {\n        innerHTML: _vm._s(_vm.markdownToHtml(message.text))\n      }\n    }), _c(\"div\", {\n      staticClass: \"message-time\"\n    }, [_vm._v(_vm._s(_vm.formatTime(message.timestamp)))])]);\n  }), _vm.isWaitingForAI ? _c(\"div\", {\n    staticClass: \"message-bubble ai-message\"\n  }, [_c(\"div\", {\n    staticClass: \"message-content\"\n  }, [_c(\"span\", {\n    staticClass: \"ai-loading\"\n  }, [_c(\"span\", {\n    staticClass: \"dot\"\n  }), _c(\"span\", {\n    staticClass: \"dot\"\n  }), _c(\"span\", {\n    staticClass: \"dot\"\n  })]), _c(\"span\", {\n    staticStyle: {\n      \"margin-left\": \"10px\"\n    }\n  }, [_vm._v(\"正在思考中...\")])]), _c(\"div\", {\n    staticClass: \"message-time\"\n  }, [_vm._v(\"问泉\")])]) : _vm._e()], 2), _c(\"div\", {\n    staticClass: \"input-area\",\n    staticStyle: {\n      display: \"flex\",\n      gap: \"10px\",\n      \"align-items\": \"center\"\n    }\n  }, [_c(\"el-input\", {\n    staticStyle: {\n      flex: \"1\",\n      \"border-radius\": \"24px\",\n      padding: \"12px 20px\"\n    },\n    attrs: {\n      type: \"textarea\",\n      rows: 1,\n      placeholder: \"输入你的问题...\",\n      resize: \"none\"\n    },\n    nativeOn: {\n      keyup: function ($event) {\n        if (!$event.type.indexOf(\"key\") && _vm._k($event.keyCode, \"enter\", 13, $event.key, \"Enter\")) return null;\n        return _vm.sendMessage.apply(null, arguments);\n      }\n    },\n    model: {\n      value: _vm.inputMessage,\n      callback: function ($$v) {\n        _vm.inputMessage = $$v;\n      },\n      expression: \"inputMessage\"\n    }\n  }), _c(\"el-button\", {\n    staticStyle: {\n      background: \"linear-gradient(135deg, #f5f1e8 0%, #e8dfc8 100%)\",\n      border: \"none\",\n      \"border-radius\": \"24px\",\n      padding: \"12px 24px\",\n      \"font-weight\": \"500\",\n      \"letter-spacing\": \"1px\",\n      \"box-shadow\": \"0 2px 6px rgba(139, 115, 85, 0.15)\",\n      transition: \"all 0.3s ease\",\n      height: \"48px\",\n      color: \"#8b7355\",\n      \"min-width\": \"90px\",\n      \"font-size\": \"15px\",\n      \"margin-left\": \"0\",\n      display: \"flex\",\n      \"align-items\": \"center\",\n      \"justify-content\": \"center\"\n    },\n    attrs: {\n      type: \"default\"\n    },\n    on: {\n      click: _vm.triggerFileInput\n    }\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-folder-add\",\n    staticStyle: {\n      \"font-size\": \"26px\",\n      \"vertical-align\": \"middle\",\n      display: \"flex\",\n      \"align-items\": \"center\",\n      \"justify-content\": \"center\",\n      width: \"100%\"\n    }\n  })]), _c(\"input\", {\n    ref: \"fileInput\",\n    staticStyle: {\n      display: \"none\"\n    },\n    attrs: {\n      type: \"file\"\n    },\n    on: {\n      change: _vm.handleFileChange\n    }\n  }), _vm.selectedFileName ? _c(\"span\", {\n    staticStyle: {\n      color: \"#8b7355\",\n      \"font-size\": \"14px\",\n      \"margin-left\": \"4px\",\n      \"margin-right\": \"8px\",\n      \"max-width\": \"120px\",\n      overflow: \"hidden\",\n      \"text-overflow\": \"ellipsis\",\n      \"white-space\": \"nowrap\",\n      display: \"inline-block\"\n    }\n  }, [_vm._v(_vm._s(_vm.selectedFileName))]) : _vm._e(), _c(\"el-button\", {\n    staticClass: \"send-button\",\n    staticStyle: {\n      background: \"linear-gradient(135deg, #8b7355 0%, #a0866b 100%)\",\n      border: \"none\",\n      \"border-radius\": \"24px\",\n      padding: \"12px 24px\",\n      \"font-weight\": \"500\",\n      \"letter-spacing\": \"1px\",\n      \"box-shadow\": \"0 2px 6px rgba(139, 115, 85, 0.3)\",\n      transition: \"all 0.3s ease\",\n      height: \"48px\"\n    },\n    attrs: {\n      type: \"primary\",\n      disabled: !_vm.inputMessage.trim()\n    },\n    on: {\n      click: _vm.sendMessage\n    }\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-s-promotion\",\n    staticStyle: {\n      \"margin-right\": \"6px\"\n    }\n  }), _vm._v(\" 发送 \")])], 1)])])], 1)], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticStyle", "height", "background", "attrs", "width", "gutter", "color", "padding", "display", "staticClass", "_v", "_s", "$store", "getters", "getSelectedPrjName", "margin", "index", "on", "click", "gotoSummarize", "gotoTest", "transition", "gotoPrj", "position", "bottom", "left", "size", "userInitial", "overflow", "username", "border", "_l", "messages", "message", "key", "class", "isUser", "domProps", "innerHTML", "markdownToHtml", "text", "formatTime", "timestamp", "isWaitingForAI", "_e", "gap", "flex", "type", "rows", "placeholder", "resize", "nativeOn", "keyup", "$event", "indexOf", "_k", "keyCode", "sendMessage", "apply", "arguments", "model", "value", "inputMessage", "callback", "$$v", "expression", "triggerFileInput", "ref", "change", "handleFileChange", "selected<PERSON><PERSON><PERSON><PERSON>", "disabled", "trim", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/问泉/try1/Inquiry-Spring/inquiryspring-front/src/views/mainPage/chatPage.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"el-container\",\n    {\n      staticStyle: {\n        height: \"100vh\",\n        background: \"linear-gradient(135deg, #f5f1e8 0%, #f0e6d2 100%)\",\n      },\n    },\n    [\n      _c(\n        \"el-aside\",\n        {\n          staticStyle: {\n            background: \"linear-gradient(to bottom, #e8dfc8, #d8cfb8)\",\n            \"border-right\": \"1px solid #d4c9a8\",\n            \"border-radius\": \"0 12px 12px 0\",\n            \"box-shadow\": \"2px 0 10px rgba(0,0,0,0.1)\",\n            \"overflow-x\": \"hidden\",\n          },\n          attrs: { width: \"240px\" },\n        },\n        [\n          _c(\"el-row\", { attrs: { gutter: 20 } }, [\n            _c(\n              \"div\",\n              {\n                staticStyle: {\n                  color: \"#5a4a3a\",\n                  padding: \"15px\",\n                  \"font-size\": \"18px\",\n                  \"font-weight\": \"bold\",\n                  display: \"flex\",\n                  \"flex-direction\": \"column\",\n                  \"align-items\": \"center\",\n                },\n              },\n              [\n                _c(\"div\", [\n                  _c(\"i\", {\n                    staticClass: \"el-icon-connection\",\n                    staticStyle: { \"margin-right\": \"8px\", color: \"#8b7355\" },\n                  }),\n                  _c(\"span\", [_vm._v(\"问泉-Inquiry Spring\")]),\n                ]),\n                _c(\"div\", { staticStyle: { \"margin-top\": \"20px\" } }, [\n                  _vm._v(_vm._s(this.$store.getters.getSelectedPrjName)),\n                ]),\n              ]\n            ),\n          ]),\n          _c(\n            \"el-menu\",\n            {\n              attrs: {\n                \"background-color\": \"#e8dfc8\",\n                \"text-color\": \"#5a4a3a\",\n                \"active-text-color\": \"#ffffff\",\n                \"default-active\": \"1\",\n              },\n            },\n            [\n              _c(\n                \"el-menu-item\",\n                {\n                  staticStyle: {\n                    \"border-radius\": \"8px\",\n                    margin: \"0 8px\",\n                    width: \"calc(100% - 16px)\",\n                    background:\n                      \"linear-gradient(135deg, #5a4a3a 0%, #3a2e24 100%)\",\n                    color: \"white\",\n                    \"box-shadow\": \"0 2px 8px rgba(90, 74, 58, 0.3)\",\n                  },\n                  attrs: { index: \"1\" },\n                },\n                [\n                  _c(\"i\", {\n                    staticClass: \"el-icon-chat-dot-round\",\n                    staticStyle: { color: \"white\" },\n                  }),\n                  _c(\"span\", [_vm._v(\"智能答疑\")]),\n                ]\n              ),\n              _c(\n                \"el-menu-item\",\n                {\n                  staticStyle: {\n                    \"border-radius\": \"8px\",\n                    margin: \"0 8px\",\n                    width: \"calc(100% - 16px)\",\n                  },\n                  attrs: { index: \"2\" },\n                  on: { click: _vm.gotoSummarize },\n                },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-notebook-2\" }),\n                  _c(\"span\", [_vm._v(\"智慧总结\")]),\n                ]\n              ),\n              _c(\n                \"el-menu-item\",\n                {\n                  staticStyle: {\n                    \"border-radius\": \"8px\",\n                    margin: \"0 8px\",\n                    width: \"calc(100% - 16px)\",\n                  },\n                  attrs: { index: \"3\" },\n                  on: { click: _vm.gotoTest },\n                },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-edit\" }),\n                  _c(\"span\", [_vm._v(\"生成小测\")]),\n                ]\n              ),\n              _c(\n                \"el-menu-item\",\n                {\n                  staticStyle: {\n                    \"border-radius\": \"8px\",\n                    margin: \"8px\",\n                    width: \"calc(100% - 16px)\",\n                    transition: \"all 0.3s\",\n                  },\n                  on: { click: _vm.gotoPrj },\n                },\n                [\n                  _c(\"i\", {\n                    staticClass: \"el-icon-folder-add\",\n                    staticStyle: { color: \"#8b7355\" },\n                  }),\n                  _c(\"span\", [_vm._v(\"管理学习项目\")]),\n                ]\n              ),\n            ],\n            1\n          ),\n          _c(\n            \"div\",\n            {\n              staticClass: \"user-info\",\n              staticStyle: {\n                position: \"fixed\",\n                bottom: \"0\",\n                left: \"0\",\n                width: \"240px\",\n                padding: \"15px\",\n                \"border-top\": \"1px solid #e0d6c2\",\n                background: \"#f1e9dd\",\n              },\n            },\n            [\n              _c(\n                \"div\",\n                {\n                  staticStyle: {\n                    display: \"flex\",\n                    \"align-items\": \"center\",\n                    padding: \"10px\",\n                  },\n                },\n                [\n                  _c(\n                    \"el-avatar\",\n                    {\n                      staticStyle: {\n                        background: \"#8b7355\",\n                        \"margin-right\": \"10px\",\n                      },\n                      attrs: { size: 40 },\n                    },\n                    [_vm._v(\" \" + _vm._s(_vm.userInitial) + \" \")]\n                  ),\n                  _c(\"div\", [\n                    _c(\n                      \"div\",\n                      {\n                        staticStyle: {\n                          color: \"#5a4a3a\",\n                          \"font-weight\": \"bold\",\n                          \"font-size\": \"14px\",\n                          \"white-space\": \"nowrap\",\n                          overflow: \"hidden\",\n                          \"text-overflow\": \"ellipsis\",\n                          \"max-width\": \"150px\",\n                        },\n                      },\n                      [_vm._v(_vm._s(_vm.username))]\n                    ),\n                    _c(\n                      \"div\",\n                      {\n                        staticStyle: { color: \"#8b7355\", \"font-size\": \"12px\" },\n                      },\n                      [_vm._v(\"已登录\")]\n                    ),\n                  ]),\n                ],\n                1\n              ),\n            ]\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"el-container\",\n        [\n          _c(\n            \"el-main\",\n            {\n              staticStyle: {\n                padding: \"20px\",\n                display: \"flex\",\n                \"flex-direction\": \"column\",\n                height: \"100%\",\n                \"background-color\": \"rgba(255,255,255,0.7)\",\n                \"border-radius\": \"16px\",\n                margin: \"20px\",\n                \"box-shadow\": \"0 4px 20px rgba(0,0,0,0.08)\",\n                border: \"1px solid rgba(139, 115, 85, 0.1)\",\n              },\n            },\n            [\n              _c(\"div\", { staticClass: \"chat-container\" }, [\n                _c(\n                  \"div\",\n                  { staticClass: \"message-list\" },\n                  [\n                    _vm._l(_vm.messages, function (message, index) {\n                      return _c(\n                        \"div\",\n                        {\n                          key: index,\n                          class: [\n                            \"message-bubble\",\n                            message.isUser ? \"user-message\" : \"ai-message\",\n                          ],\n                        },\n                        [\n                          _c(\"div\", {\n                            staticClass: \"message-content\",\n                            domProps: {\n                              innerHTML: _vm._s(\n                                _vm.markdownToHtml(message.text)\n                              ),\n                            },\n                          }),\n                          _c(\"div\", { staticClass: \"message-time\" }, [\n                            _vm._v(_vm._s(_vm.formatTime(message.timestamp))),\n                          ]),\n                        ]\n                      )\n                    }),\n                    _vm.isWaitingForAI\n                      ? _c(\n                          \"div\",\n                          { staticClass: \"message-bubble ai-message\" },\n                          [\n                            _c(\"div\", { staticClass: \"message-content\" }, [\n                              _c(\"span\", { staticClass: \"ai-loading\" }, [\n                                _c(\"span\", { staticClass: \"dot\" }),\n                                _c(\"span\", { staticClass: \"dot\" }),\n                                _c(\"span\", { staticClass: \"dot\" }),\n                              ]),\n                              _c(\n                                \"span\",\n                                { staticStyle: { \"margin-left\": \"10px\" } },\n                                [_vm._v(\"正在思考中...\")]\n                              ),\n                            ]),\n                            _c(\"div\", { staticClass: \"message-time\" }, [\n                              _vm._v(\"问泉\"),\n                            ]),\n                          ]\n                        )\n                      : _vm._e(),\n                  ],\n                  2\n                ),\n                _c(\n                  \"div\",\n                  {\n                    staticClass: \"input-area\",\n                    staticStyle: {\n                      display: \"flex\",\n                      gap: \"10px\",\n                      \"align-items\": \"center\",\n                    },\n                  },\n                  [\n                    _c(\"el-input\", {\n                      staticStyle: {\n                        flex: \"1\",\n                        \"border-radius\": \"24px\",\n                        padding: \"12px 20px\",\n                      },\n                      attrs: {\n                        type: \"textarea\",\n                        rows: 1,\n                        placeholder: \"输入你的问题...\",\n                        resize: \"none\",\n                      },\n                      nativeOn: {\n                        keyup: function ($event) {\n                          if (\n                            !$event.type.indexOf(\"key\") &&\n                            _vm._k(\n                              $event.keyCode,\n                              \"enter\",\n                              13,\n                              $event.key,\n                              \"Enter\"\n                            )\n                          )\n                            return null\n                          return _vm.sendMessage.apply(null, arguments)\n                        },\n                      },\n                      model: {\n                        value: _vm.inputMessage,\n                        callback: function ($$v) {\n                          _vm.inputMessage = $$v\n                        },\n                        expression: \"inputMessage\",\n                      },\n                    }),\n                    _c(\n                      \"el-button\",\n                      {\n                        staticStyle: {\n                          background:\n                            \"linear-gradient(135deg, #f5f1e8 0%, #e8dfc8 100%)\",\n                          border: \"none\",\n                          \"border-radius\": \"24px\",\n                          padding: \"12px 24px\",\n                          \"font-weight\": \"500\",\n                          \"letter-spacing\": \"1px\",\n                          \"box-shadow\": \"0 2px 6px rgba(139, 115, 85, 0.15)\",\n                          transition: \"all 0.3s ease\",\n                          height: \"48px\",\n                          color: \"#8b7355\",\n                          \"min-width\": \"90px\",\n                          \"font-size\": \"15px\",\n                          \"margin-left\": \"0\",\n                          display: \"flex\",\n                          \"align-items\": \"center\",\n                          \"justify-content\": \"center\",\n                        },\n                        attrs: { type: \"default\" },\n                        on: { click: _vm.triggerFileInput },\n                      },\n                      [\n                        _c(\"i\", {\n                          staticClass: \"el-icon-folder-add\",\n                          staticStyle: {\n                            \"font-size\": \"26px\",\n                            \"vertical-align\": \"middle\",\n                            display: \"flex\",\n                            \"align-items\": \"center\",\n                            \"justify-content\": \"center\",\n                            width: \"100%\",\n                          },\n                        }),\n                      ]\n                    ),\n                    _c(\"input\", {\n                      ref: \"fileInput\",\n                      staticStyle: { display: \"none\" },\n                      attrs: { type: \"file\" },\n                      on: { change: _vm.handleFileChange },\n                    }),\n                    _vm.selectedFileName\n                      ? _c(\n                          \"span\",\n                          {\n                            staticStyle: {\n                              color: \"#8b7355\",\n                              \"font-size\": \"14px\",\n                              \"margin-left\": \"4px\",\n                              \"margin-right\": \"8px\",\n                              \"max-width\": \"120px\",\n                              overflow: \"hidden\",\n                              \"text-overflow\": \"ellipsis\",\n                              \"white-space\": \"nowrap\",\n                              display: \"inline-block\",\n                            },\n                          },\n                          [_vm._v(_vm._s(_vm.selectedFileName))]\n                        )\n                      : _vm._e(),\n                    _c(\n                      \"el-button\",\n                      {\n                        staticClass: \"send-button\",\n                        staticStyle: {\n                          background:\n                            \"linear-gradient(135deg, #8b7355 0%, #a0866b 100%)\",\n                          border: \"none\",\n                          \"border-radius\": \"24px\",\n                          padding: \"12px 24px\",\n                          \"font-weight\": \"500\",\n                          \"letter-spacing\": \"1px\",\n                          \"box-shadow\": \"0 2px 6px rgba(139, 115, 85, 0.3)\",\n                          transition: \"all 0.3s ease\",\n                          height: \"48px\",\n                        },\n                        attrs: {\n                          type: \"primary\",\n                          disabled: !_vm.inputMessage.trim(),\n                        },\n                        on: { click: _vm.sendMessage },\n                      },\n                      [\n                        _c(\"i\", {\n                          staticClass: \"el-icon-s-promotion\",\n                          staticStyle: { \"margin-right\": \"6px\" },\n                        }),\n                        _vm._v(\" 发送 \"),\n                      ]\n                    ),\n                  ],\n                  1\n                ),\n              ]),\n            ]\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,cAAc,EACd;IACEE,WAAW,EAAE;MACXC,MAAM,EAAE,OAAO;MACfC,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACEJ,EAAE,CACA,UAAU,EACV;IACEE,WAAW,EAAE;MACXE,UAAU,EAAE,8CAA8C;MAC1D,cAAc,EAAE,mBAAmB;MACnC,eAAe,EAAE,eAAe;MAChC,YAAY,EAAE,4BAA4B;MAC1C,YAAY,EAAE;IAChB,CAAC;IACDC,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAQ;EAC1B,CAAC,EACD,CACEN,EAAE,CAAC,QAAQ,EAAE;IAAEK,KAAK,EAAE;MAAEE,MAAM,EAAE;IAAG;EAAE,CAAC,EAAE,CACtCP,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE;MACXM,KAAK,EAAE,SAAS;MAChBC,OAAO,EAAE,MAAM;MACf,WAAW,EAAE,MAAM;MACnB,aAAa,EAAE,MAAM;MACrBC,OAAO,EAAE,MAAM;MACf,gBAAgB,EAAE,QAAQ;MAC1B,aAAa,EAAE;IACjB;EACF,CAAC,EACD,CACEV,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,GAAG,EAAE;IACNW,WAAW,EAAE,oBAAoB;IACjCT,WAAW,EAAE;MAAE,cAAc,EAAE,KAAK;MAAEM,KAAK,EAAE;IAAU;EACzD,CAAC,CAAC,EACFR,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACa,EAAE,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAC1C,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;MAAE,YAAY,EAAE;IAAO;EAAE,CAAC,EAAE,CACnDH,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,EAAE,CAAC,IAAI,CAACC,MAAM,CAACC,OAAO,CAACC,kBAAkB,CAAC,CAAC,CACvD,CAAC,CAEN,CAAC,CACF,CAAC,EACFhB,EAAE,CACA,SAAS,EACT;IACEK,KAAK,EAAE;MACL,kBAAkB,EAAE,SAAS;MAC7B,YAAY,EAAE,SAAS;MACvB,mBAAmB,EAAE,SAAS;MAC9B,gBAAgB,EAAE;IACpB;EACF,CAAC,EACD,CACEL,EAAE,CACA,cAAc,EACd;IACEE,WAAW,EAAE;MACX,eAAe,EAAE,KAAK;MACtBe,MAAM,EAAE,OAAO;MACfX,KAAK,EAAE,mBAAmB;MAC1BF,UAAU,EACR,mDAAmD;MACrDI,KAAK,EAAE,OAAO;MACd,YAAY,EAAE;IAChB,CAAC;IACDH,KAAK,EAAE;MAAEa,KAAK,EAAE;IAAI;EACtB,CAAC,EACD,CACElB,EAAE,CAAC,GAAG,EAAE;IACNW,WAAW,EAAE,wBAAwB;IACrCT,WAAW,EAAE;MAAEM,KAAK,EAAE;IAAQ;EAChC,CAAC,CAAC,EACFR,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACa,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAEhC,CAAC,EACDZ,EAAE,CACA,cAAc,EACd;IACEE,WAAW,EAAE;MACX,eAAe,EAAE,KAAK;MACtBe,MAAM,EAAE,OAAO;MACfX,KAAK,EAAE;IACT,CAAC;IACDD,KAAK,EAAE;MAAEa,KAAK,EAAE;IAAI,CAAC;IACrBC,EAAE,EAAE;MAAEC,KAAK,EAAErB,GAAG,CAACsB;IAAc;EACjC,CAAC,EACD,CACErB,EAAE,CAAC,GAAG,EAAE;IAAEW,WAAW,EAAE;EAAqB,CAAC,CAAC,EAC9CX,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACa,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAEhC,CAAC,EACDZ,EAAE,CACA,cAAc,EACd;IACEE,WAAW,EAAE;MACX,eAAe,EAAE,KAAK;MACtBe,MAAM,EAAE,OAAO;MACfX,KAAK,EAAE;IACT,CAAC;IACDD,KAAK,EAAE;MAAEa,KAAK,EAAE;IAAI,CAAC;IACrBC,EAAE,EAAE;MAAEC,KAAK,EAAErB,GAAG,CAACuB;IAAS;EAC5B,CAAC,EACD,CACEtB,EAAE,CAAC,GAAG,EAAE;IAAEW,WAAW,EAAE;EAAe,CAAC,CAAC,EACxCX,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACa,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAEhC,CAAC,EACDZ,EAAE,CACA,cAAc,EACd;IACEE,WAAW,EAAE;MACX,eAAe,EAAE,KAAK;MACtBe,MAAM,EAAE,KAAK;MACbX,KAAK,EAAE,mBAAmB;MAC1BiB,UAAU,EAAE;IACd,CAAC;IACDJ,EAAE,EAAE;MAAEC,KAAK,EAAErB,GAAG,CAACyB;IAAQ;EAC3B,CAAC,EACD,CACExB,EAAE,CAAC,GAAG,EAAE;IACNW,WAAW,EAAE,oBAAoB;IACjCT,WAAW,EAAE;MAAEM,KAAK,EAAE;IAAU;EAClC,CAAC,CAAC,EACFR,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACa,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAElC,CAAC,CACF,EACD,CACF,CAAC,EACDZ,EAAE,CACA,KAAK,EACL;IACEW,WAAW,EAAE,WAAW;IACxBT,WAAW,EAAE;MACXuB,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,GAAG;MACXC,IAAI,EAAE,GAAG;MACTrB,KAAK,EAAE,OAAO;MACdG,OAAO,EAAE,MAAM;MACf,YAAY,EAAE,mBAAmB;MACjCL,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACEJ,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE;MACXQ,OAAO,EAAE,MAAM;MACf,aAAa,EAAE,QAAQ;MACvBD,OAAO,EAAE;IACX;EACF,CAAC,EACD,CACET,EAAE,CACA,WAAW,EACX;IACEE,WAAW,EAAE;MACXE,UAAU,EAAE,SAAS;MACrB,cAAc,EAAE;IAClB,CAAC;IACDC,KAAK,EAAE;MAAEuB,IAAI,EAAE;IAAG;EACpB,CAAC,EACD,CAAC7B,GAAG,CAACa,EAAE,CAAC,GAAG,GAAGb,GAAG,CAACc,EAAE,CAACd,GAAG,CAAC8B,WAAW,CAAC,GAAG,GAAG,CAAC,CAC9C,CAAC,EACD7B,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE;MACXM,KAAK,EAAE,SAAS;MAChB,aAAa,EAAE,MAAM;MACrB,WAAW,EAAE,MAAM;MACnB,aAAa,EAAE,QAAQ;MACvBsB,QAAQ,EAAE,QAAQ;MAClB,eAAe,EAAE,UAAU;MAC3B,WAAW,EAAE;IACf;EACF,CAAC,EACD,CAAC/B,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,EAAE,CAACd,GAAG,CAACgC,QAAQ,CAAC,CAAC,CAC/B,CAAC,EACD/B,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE;MAAEM,KAAK,EAAE,SAAS;MAAE,WAAW,EAAE;IAAO;EACvD,CAAC,EACD,CAACT,GAAG,CAACa,EAAE,CAAC,KAAK,CAAC,CAChB,CAAC,CACF,CAAC,CACH,EACD,CACF,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,EACDZ,EAAE,CACA,cAAc,EACd,CACEA,EAAE,CACA,SAAS,EACT;IACEE,WAAW,EAAE;MACXO,OAAO,EAAE,MAAM;MACfC,OAAO,EAAE,MAAM;MACf,gBAAgB,EAAE,QAAQ;MAC1BP,MAAM,EAAE,MAAM;MACd,kBAAkB,EAAE,uBAAuB;MAC3C,eAAe,EAAE,MAAM;MACvBc,MAAM,EAAE,MAAM;MACd,YAAY,EAAE,6BAA6B;MAC3Ce,MAAM,EAAE;IACV;EACF,CAAC,EACD,CACEhC,EAAE,CAAC,KAAK,EAAE;IAAEW,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CX,EAAE,CACA,KAAK,EACL;IAAEW,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEZ,GAAG,CAACkC,EAAE,CAAClC,GAAG,CAACmC,QAAQ,EAAE,UAAUC,OAAO,EAAEjB,KAAK,EAAE;IAC7C,OAAOlB,EAAE,CACP,KAAK,EACL;MACEoC,GAAG,EAAElB,KAAK;MACVmB,KAAK,EAAE,CACL,gBAAgB,EAChBF,OAAO,CAACG,MAAM,GAAG,cAAc,GAAG,YAAY;IAElD,CAAC,EACD,CACEtC,EAAE,CAAC,KAAK,EAAE;MACRW,WAAW,EAAE,iBAAiB;MAC9B4B,QAAQ,EAAE;QACRC,SAAS,EAAEzC,GAAG,CAACc,EAAE,CACfd,GAAG,CAAC0C,cAAc,CAACN,OAAO,CAACO,IAAI,CACjC;MACF;IACF,CAAC,CAAC,EACF1C,EAAE,CAAC,KAAK,EAAE;MAAEW,WAAW,EAAE;IAAe,CAAC,EAAE,CACzCZ,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,EAAE,CAACd,GAAG,CAAC4C,UAAU,CAACR,OAAO,CAACS,SAAS,CAAC,CAAC,CAAC,CAClD,CAAC,CAEN,CAAC;EACH,CAAC,CAAC,EACF7C,GAAG,CAAC8C,cAAc,GACd7C,EAAE,CACA,KAAK,EACL;IAAEW,WAAW,EAAE;EAA4B,CAAC,EAC5C,CACEX,EAAE,CAAC,KAAK,EAAE;IAAEW,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CX,EAAE,CAAC,MAAM,EAAE;IAAEW,WAAW,EAAE;EAAa,CAAC,EAAE,CACxCX,EAAE,CAAC,MAAM,EAAE;IAAEW,WAAW,EAAE;EAAM,CAAC,CAAC,EAClCX,EAAE,CAAC,MAAM,EAAE;IAAEW,WAAW,EAAE;EAAM,CAAC,CAAC,EAClCX,EAAE,CAAC,MAAM,EAAE;IAAEW,WAAW,EAAE;EAAM,CAAC,CAAC,CACnC,CAAC,EACFX,EAAE,CACA,MAAM,EACN;IAAEE,WAAW,EAAE;MAAE,aAAa,EAAE;IAAO;EAAE,CAAC,EAC1C,CAACH,GAAG,CAACa,EAAE,CAAC,UAAU,CAAC,CACrB,CAAC,CACF,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;IAAEW,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCZ,GAAG,CAACa,EAAE,CAAC,IAAI,CAAC,CACb,CAAC,CAEN,CAAC,GACDb,GAAG,CAAC+C,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,EACD9C,EAAE,CACA,KAAK,EACL;IACEW,WAAW,EAAE,YAAY;IACzBT,WAAW,EAAE;MACXQ,OAAO,EAAE,MAAM;MACfqC,GAAG,EAAE,MAAM;MACX,aAAa,EAAE;IACjB;EACF,CAAC,EACD,CACE/C,EAAE,CAAC,UAAU,EAAE;IACbE,WAAW,EAAE;MACX8C,IAAI,EAAE,GAAG;MACT,eAAe,EAAE,MAAM;MACvBvC,OAAO,EAAE;IACX,CAAC;IACDJ,KAAK,EAAE;MACL4C,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,CAAC;MACPC,WAAW,EAAE,WAAW;MACxBC,MAAM,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACRC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,IACE,CAACA,MAAM,CAACN,IAAI,CAACO,OAAO,CAAC,KAAK,CAAC,IAC3BzD,GAAG,CAAC0D,EAAE,CACJF,MAAM,CAACG,OAAO,EACd,OAAO,EACP,EAAE,EACFH,MAAM,CAACnB,GAAG,EACV,OACF,CAAC,EAED,OAAO,IAAI;QACb,OAAOrC,GAAG,CAAC4D,WAAW,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC/C;IACF,CAAC;IACDC,KAAK,EAAE;MACLC,KAAK,EAAEhE,GAAG,CAACiE,YAAY;MACvBC,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvBnE,GAAG,CAACiE,YAAY,GAAGE,GAAG;MACxB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,EACFnE,EAAE,CACA,WAAW,EACX;IACEE,WAAW,EAAE;MACXE,UAAU,EACR,mDAAmD;MACrD4B,MAAM,EAAE,MAAM;MACd,eAAe,EAAE,MAAM;MACvBvB,OAAO,EAAE,WAAW;MACpB,aAAa,EAAE,KAAK;MACpB,gBAAgB,EAAE,KAAK;MACvB,YAAY,EAAE,oCAAoC;MAClDc,UAAU,EAAE,eAAe;MAC3BpB,MAAM,EAAE,MAAM;MACdK,KAAK,EAAE,SAAS;MAChB,WAAW,EAAE,MAAM;MACnB,WAAW,EAAE,MAAM;MACnB,aAAa,EAAE,GAAG;MAClBE,OAAO,EAAE,MAAM;MACf,aAAa,EAAE,QAAQ;MACvB,iBAAiB,EAAE;IACrB,CAAC;IACDL,KAAK,EAAE;MAAE4C,IAAI,EAAE;IAAU,CAAC;IAC1B9B,EAAE,EAAE;MAAEC,KAAK,EAAErB,GAAG,CAACqE;IAAiB;EACpC,CAAC,EACD,CACEpE,EAAE,CAAC,GAAG,EAAE;IACNW,WAAW,EAAE,oBAAoB;IACjCT,WAAW,EAAE;MACX,WAAW,EAAE,MAAM;MACnB,gBAAgB,EAAE,QAAQ;MAC1BQ,OAAO,EAAE,MAAM;MACf,aAAa,EAAE,QAAQ;MACvB,iBAAiB,EAAE,QAAQ;MAC3BJ,KAAK,EAAE;IACT;EACF,CAAC,CAAC,CAEN,CAAC,EACDN,EAAE,CAAC,OAAO,EAAE;IACVqE,GAAG,EAAE,WAAW;IAChBnE,WAAW,EAAE;MAAEQ,OAAO,EAAE;IAAO,CAAC;IAChCL,KAAK,EAAE;MAAE4C,IAAI,EAAE;IAAO,CAAC;IACvB9B,EAAE,EAAE;MAAEmD,MAAM,EAAEvE,GAAG,CAACwE;IAAiB;EACrC,CAAC,CAAC,EACFxE,GAAG,CAACyE,gBAAgB,GAChBxE,EAAE,CACA,MAAM,EACN;IACEE,WAAW,EAAE;MACXM,KAAK,EAAE,SAAS;MAChB,WAAW,EAAE,MAAM;MACnB,aAAa,EAAE,KAAK;MACpB,cAAc,EAAE,KAAK;MACrB,WAAW,EAAE,OAAO;MACpBsB,QAAQ,EAAE,QAAQ;MAClB,eAAe,EAAE,UAAU;MAC3B,aAAa,EAAE,QAAQ;MACvBpB,OAAO,EAAE;IACX;EACF,CAAC,EACD,CAACX,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,EAAE,CAACd,GAAG,CAACyE,gBAAgB,CAAC,CAAC,CACvC,CAAC,GACDzE,GAAG,CAAC+C,EAAE,CAAC,CAAC,EACZ9C,EAAE,CACA,WAAW,EACX;IACEW,WAAW,EAAE,aAAa;IAC1BT,WAAW,EAAE;MACXE,UAAU,EACR,mDAAmD;MACrD4B,MAAM,EAAE,MAAM;MACd,eAAe,EAAE,MAAM;MACvBvB,OAAO,EAAE,WAAW;MACpB,aAAa,EAAE,KAAK;MACpB,gBAAgB,EAAE,KAAK;MACvB,YAAY,EAAE,mCAAmC;MACjDc,UAAU,EAAE,eAAe;MAC3BpB,MAAM,EAAE;IACV,CAAC;IACDE,KAAK,EAAE;MACL4C,IAAI,EAAE,SAAS;MACfwB,QAAQ,EAAE,CAAC1E,GAAG,CAACiE,YAAY,CAACU,IAAI,CAAC;IACnC,CAAC;IACDvD,EAAE,EAAE;MAAEC,KAAK,EAAErB,GAAG,CAAC4D;IAAY;EAC/B,CAAC,EACD,CACE3D,EAAE,CAAC,GAAG,EAAE;IACNW,WAAW,EAAE,qBAAqB;IAClCT,WAAW,EAAE;MAAE,cAAc,EAAE;IAAM;EACvC,CAAC,CAAC,EACFH,GAAG,CAACa,EAAE,CAAC,MAAM,CAAC,CAElB,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CAEN,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAI+D,eAAe,GAAG,EAAE;AACxB7E,MAAM,CAAC8E,aAAa,GAAG,IAAI;AAE3B,SAAS9E,MAAM,EAAE6E,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}