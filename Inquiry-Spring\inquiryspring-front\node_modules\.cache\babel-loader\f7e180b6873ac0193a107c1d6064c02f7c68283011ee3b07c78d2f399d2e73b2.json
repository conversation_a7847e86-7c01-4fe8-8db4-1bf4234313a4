{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport axios from 'axios';\nimport { Marked } from 'marked';\nimport { markedHighlight } from \"marked-highlight\";\nimport hljs from 'highlight.js/lib/core';\nexport default {\n  data() {\n    return {\n      summarizeMsg: \">tips:\\n > - 选择你要总结的文档\\n> - 生成总结\",\n      url: this.HOST + '/summarize/',\n      md: '# hhh',\n      // 动画相关\n      displayLines: [],\n      currentLine: \"\",\n      typingTimer: null,\n      lineIdx: 0,\n      typingLines: [],\n      loading: false,\n      // 新增加载动画状态\n      uploadedFiles: [{\n        name: \"ccf2012-b.pdf\"\n      }],\n      // 新增：已上传文件列表\n      // currentFiles:[\"Big_Data_Quality_A_Survey.pdf\"],\n      selectedFileRow: null,\n      // 新增：当前选中的文件行对象\n      selectedFileName: '',\n      // 新增：当前选中文件名\n      username: '',\n      userInitial: ''\n    };\n  },\n  created() {\n    // 检查localStorage中是否有用户信息\n    const userInfo = localStorage.getItem('userInfo');\n    // 将JSON字符串转换为对象\n    const parsedUserInfo = JSON.parse(userInfo);\n    // 触发Vuex action来更新store中的用户信息\n    this.$store.dispatch('restoreUserInfo', parsedUserInfo);\n\n    // 获取当前用户信息\n    const user = this.$store.getters.getUserInfo;\n    if (user && user.username) {\n      this.username = user.username;\n      this.userInitial = user.username.charAt(0).toUpperCase();\n    } else {\n      this.username = '未登录';\n      this.userInitial = '?';\n    }\n  },\n  mounted() {\n    this.startLineAnimation(this.summarizeMsg);\n  },\n  computed: {\n    animatedHtml() {\n      let arr = this.displayLines.slice();\n      if (this.currentLine) {\n        arr.push(this.currentLine + '<span class=\"typing-cursor\">|</span>');\n      }\n      return this.markdownToHtml(arr.join('\\n'));\n    }\n  },\n  methods: {\n    markdownToHtml(message) {\n      if (!message) return '';\n      const marked = new Marked(markedHighlight({\n        pedantic: false,\n        gfm: true,\n        breaks: true,\n        smartLists: true,\n        xhtml: true,\n        async: false,\n        langPrefix: 'hljs language-',\n        emptyLangClass: 'no-lang',\n        highlight: code => {\n          return hljs.highlightAuto(code).value;\n        }\n      }));\n      return marked.parse(message);\n    },\n    gotoChat() {\n      this.$router.push({\n        path: '/chat'\n      });\n    },\n    gotoTest() {\n      this.$router.push({\n        path: '/test'\n      });\n    },\n    output() {\n      window.alert(this.summarizeMsg);\n    },\n    gotoPrj() {\n      this.$router.push({\n        path: '/project'\n      });\n    },\n    // 上传文件前的校验\n    beforeUpload(file) {\n      const isLt10M = file.size / 1024 / 1024 < 10;\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过10MB!');\n      }\n      return isLt10M;\n    },\n    // 文件上传成功处理\n    handleUploadSuccess(response, file) {\n      console.log(response.data);\n      this.$message.success(`${file.name} 上传成功`);\n      // 新增：添加到已上传文件列表\n      this.uploadedFiles.push({\n        name: file.name\n      });\n    },\n    // 处理文件行选择\n    handleFileRowSelect(row) {\n      this.selectedFileRow = row;\n      this.selectedFileName = row ? row.name : '';\n    },\n    startLineAnimation(msg) {\n      if (this.typingTimer) {\n        clearTimeout(this.typingTimer);\n        this.typingTimer = null;\n      }\n      this.displayLines = [];\n      this.currentLine = '';\n      this.lineIdx = 0;\n      this.typingLines = msg.split(/\\r?\\n/);\n      this.typeNextChar();\n    },\n    typeNextChar() {\n      if (this.lineIdx >= this.typingLines.length) {\n        this.currentLine = '';\n        this.displayLines = this.typingLines.slice();\n        return;\n      }\n      const line = this.typingLines[this.lineIdx];\n      if (this.currentLine.length < line.length) {\n        this.currentLine += line[this.currentLine.length];\n        this.typingTimer = setTimeout(this.typeNextChar, 10);\n      } else {\n        this.displayLines.push(this.currentLine);\n        this.currentLine = '';\n        this.lineIdx++;\n        this.typingTimer = setTimeout(this.typeNextChar, 120);\n      }\n    },\n    generateSummary() {\n      //window.alert(JSON.stringify(this.selectedFileName))\n      this.loading = true;\n      setTimeout(() => {\n        axios.get(this.url, {\n          params: {\n            fileName: this.selectedFileName\n          }\n        }).then(response => {\n          this.summarizeMsg = response.data.AIMessage;\n          this.loading = false;\n          this.startLineAnimation(this.summarizeMsg);\n        }).catch(error => {\n          this.loading = false;\n          this.$message.error('获取AI回复失败:' + error.message);\n        });\n      }, 15000);\n    }\n  }\n};", "map": {"version": 3, "names": ["axios", "Marked", "<PERSON><PERSON><PERSON><PERSON>", "hljs", "data", "summarizeMsg", "url", "HOST", "md", "displayLines", "currentLine", "typingTimer", "lineIdx", "typingLines", "loading", "uploadedFiles", "name", "selectedFileRow", "selected<PERSON><PERSON><PERSON><PERSON>", "username", "userInitial", "created", "userInfo", "localStorage", "getItem", "parsedUserInfo", "JSON", "parse", "$store", "dispatch", "user", "getters", "getUserInfo", "char<PERSON>t", "toUpperCase", "mounted", "startLineAnimation", "computed", "animatedHtml", "arr", "slice", "push", "markdownToHtml", "join", "methods", "message", "marked", "pedantic", "gfm", "breaks", "smartLists", "xhtml", "async", "langPrefix", "emptyLangClass", "highlight", "code", "highlightAuto", "value", "gotoChat", "$router", "path", "gotoTest", "output", "window", "alert", "gotoPrj", "beforeUpload", "file", "isLt10M", "size", "$message", "error", "handleUploadSuccess", "response", "console", "log", "success", "handleFileRowSelect", "row", "msg", "clearTimeout", "split", "typeNextChar", "length", "line", "setTimeout", "generateSummary", "get", "params", "fileName", "then", "AIMessage", "catch"], "sources": ["src/views/mainPage/summarizePage.vue"], "sourcesContent": ["<template>\r\n    <el-container style=\"height: 100vh; background: linear-gradient(135deg, #f5f1e8 0%, #f0e6d2 100%)\">\r\n    <el-aside width=\"240px\" style=\"background: linear-gradient(to bottom, #e8dfc8, #d8cfb8); border-right: 1px solid #d4c9a8; border-radius: 0 12px 12px 0; box-shadow: 2px 0 10px rgba(0,0,0,0.1); overflow-x: hidden\">\r\n        <el-row :gutter=\"20\">\r\n            <div style=\"color: #5a4a3a; padding: 15px; font-size: 18px; font-weight: bold; display: flex; flex-direction: column; align-items: center;\">\r\n                <div>\r\n                    <i class=\"el-icon-connection\" style=\"margin-right: 8px; color: #8b7355\"></i>\r\n                    <span>问泉-Inquiry Spring</span>\r\n                </div>\r\n                <div style=\"margin-top: 20px;\">{{ this.$store.getters.getSelectedPrjName}}</div>\r\n            </div>   \r\n        </el-row>\r\n        <el-menu \r\n            background-color=\"#e8dfc8\"\r\n            text-color=\"#5a4a3a\"\r\n            active-text-color=\"#ffffff\"\r\n            :default-active=\"'1'\"\r\n            style=\"overflow-x: hidden\">\r\n            <el-menu-item @click=\"gotoChat\" index=\"2\" style=\"border-radius: 8px; margin: 0 8px; width: calc(100% - 16px)\">\r\n                <i class=\"el-icon-chat-dot-round\"></i>\r\n                <span>智能答疑</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"1\" style=\"border-radius: 8px; margin: 0 8px; width: calc(100% - 16px); background: linear-gradient(135deg, #5a4a3a 0%, #3a2e24 100%); color: white; box-shadow: 0 2px 8px rgba(90, 74, 58, 0.3)\">\r\n                <i class=\"el-icon-notebook-2\" style=\"color: white\"></i>\r\n                <span>智慧总结</span>\r\n            </el-menu-item>\r\n            <el-menu-item @click=\"gotoTest\" index=\"3\" style=\"border-radius: 8px; margin: 8px; width: calc(100% - 16px); transition: all 0.3s\">\r\n                <i class=\"el-icon-edit\" style=\"color: #8b7355\"></i>\r\n                <span>生成小测</span>\r\n            </el-menu-item>\r\n            <el-menu-item @click=\"gotoPrj\" style=\"border-radius: 8px; margin: 8px; width: calc(100% - 16px); transition: all 0.3s\">\r\n                <i class=\"el-icon-folder-add\" style=\"color: #8b7355\"></i>\r\n                <span>管理学习项目</span>\r\n            </el-menu-item>\r\n        </el-menu>\r\n        <!-- 用户信息展示 -->\r\n        <div class=\"user-info\" style=\"position: fixed; bottom: 0; left: 0; width: 240px; padding: 15px; border-top: 1px solid #e0d6c2; background: #f1e9dd;\">\r\n            <div style=\"display: flex; align-items: center; padding: 10px;\">\r\n                <el-avatar :size=\"40\" style=\"background: #8b7355; margin-right: 10px;\">\r\n                    {{ userInitial }}\r\n                </el-avatar>\r\n                <div>\r\n                    <div style=\"color: #5a4a3a; font-weight: bold; font-size: 14px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 150px;\">{{ username }}</div>\r\n                    <div style=\"color: #8b7355; font-size: 12px;\">已登录</div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </el-aside>\r\n    \r\n    <el-container>\r\n        <el-main style=\"padding: 10px; display: flex; flex-direction: column; height: 100%; background-color: rgba(255,255,255,0.7); border-radius: 16px; margin: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); border: 1px solid rgba(139, 115, 85, 0.1)\">\r\n            <div class=\"content-container\" style=\"margin: 10px; padding: 0; background: transparent; box-shadow: none; border: none\">\r\n                <el-row :gutter=\"20\" style=\"height: 100%\">\r\n                    <el-col :span=\"7.5\" style=\"padding: 10px;\">\r\n                        <div style=\"height: 100%; padding: 20px; background: white; border-radius: 12px; box-shadow: 0 2px 12px rgba(0,0,0,0.05); display: flex; flex-direction: column;\">\r\n                            <h3 style=\"margin-bottom: 16px; color: #5a4a3a; display: flex; align-items: center; gap: 8px;\">\r\n                                上传文件\r\n                                <el-tooltip content=\"上传的学习材料或学习笔记，生成总结\" placement=\"right\">\r\n                                    <i class=\"el-icon-question\" style=\"color: #8b7355; font-size: 16px;\"></i>\r\n                                </el-tooltip>\r\n                            </h3>\r\n                            <div style=\"flex: 1; display: flex; flex-direction: column;\">\r\n                                <el-upload\r\n                                    class=\"upload-demo\"\r\n                                    show-file-list=\"false\"\r\n                                    drag\r\n                                    :action=this.url\r\n                                    multiple\r\n                                    :on-success=\"handleUploadSuccess\"\r\n                                    :before-upload=\"beforeUpload\"\r\n                                    style=\"flex: 1; display: flex; flex-direction: column;\">\r\n                                    <i class=\"el-icon-upload\" style=\"color: #8b7355; font-size: 48px; margin-bottom: 16px;\"></i>\r\n                                    <div class=\"el-upload__text\" style=\"color: #5a4a3a; font-size: 14px;\">将文件拖到此处，或<em style=\"color: #8b7355;\">点击上传</em></div>\r\n                                    <div class=\"el-upload__tip\" slot=\"tip\" style=\"color: #8b7355; margin-top: 16px;\">支持word,pdf格式</div>\r\n                                </el-upload>\r\n                                <!-- 新增：已上传文件表格，单独分区 -->\r\n                                <div v-if=\"uploadedFiles.length\" style=\"margin: 50px auto 0 auto; padding: 16px 12px; background: #f8f6f2; border-radius: 8px; box-shadow: 0 2px 8px rgba(139,115,85,0.06); border: 1px solid #e8dfc8; width: 95%; max-width: 600px; min-width: 220px; box-sizing: border-box; display: flex; flex-direction: column; align-items: center;\">\r\n                                    <div style=\"font-weight: bold; color: #8b7355; margin-bottom: 10px; font-size: 15px; letter-spacing: 1px; width: 100%; text-align: left;\">当前项目文档</div>\r\n                                    <el-table \r\n                                        :data=\"uploadedFiles\" \r\n                                        border \r\n                                        style=\"width: 100%; background: #fff;\"\r\n                                        highlight-current-row\r\n                                        @current-change=\"handleFileRowSelect\"\r\n                                        :current-row=\"selectedFileRow\"\r\n                                    >\r\n                                        <el-table-column prop=\"name\" label=\"文件名\" min-width=\"120\" align=\"left\">\r\n                                            <template slot-scope=\"scope\">\r\n                                                <i class=\"el-icon-document\" style=\"margin-right: 6px; color: #8b7355;\"></i>{{ scope.row.name }}\r\n                                            </template>\r\n                                        </el-table-column>\r\n                                    </el-table>\r\n                                </div>\r\n                                <v-btn @click=\"generateSummary\" color=\"#8b7355\" style=\"color: white; margin-top: 20px; align-self: flex-end;\">\r\n                                    立即生成\r\n                                </v-btn>\r\n                            </div>\r\n                        </div>\r\n                    </el-col>\r\n                    <el-col :span=\"16\" style=\"padding: 10px;\">\r\n                        <div style=\"height: 100%; padding: 20px; background: white; border-radius: 12px; box-shadow: 0 2px 12px rgba(0,0,0,0.05); display: flex; flex-direction: column;\">\r\n                            <div style=\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;\">\r\n                                <h3 style=\"color: #5a4a3a;\">总结内容</h3>\r\n                                <v-btn @click=\"output\" color=\"#8b7355\" style=\"color: white;\">\r\n                                    导出\r\n                                </v-btn>\r\n                            </div>\r\n                            <div class=\"markdown-container\" style=\"flex: 1; overflow-y: auto;\">\r\n                                <div v-if=\"loading\" style=\"display: flex; align-items: center; justify-content: center; height: 100%;\">\r\n                                    <span class=\"ai-loading\">\r\n                                        <span class=\"dot\"></span><span class=\"dot\"></span><span class=\"dot\"></span>\r\n                                    </span>\r\n                                </div>\r\n                                <div v-else v-html=\"animatedHtml\"></div>\r\n                            </div>\r\n                        </div>\r\n                    </el-col>\r\n                </el-row>\r\n            </div>\r\n        </el-main>\r\n    </el-container>\r\n    </el-container>\r\n</template>\r\n\r\n<style>\r\n    .el-header {\r\n        background-color: #B3C0D1;\r\n        color: #333;\r\n        line-height: 60px;\r\n    }\r\n    \r\n    .el-aside {\r\n        color: #333;\r\n    }\r\n    \r\n    .el-menu-item {\r\n        transition: all 0.3s ease;\r\n    }\r\n    \r\n    .el-menu-item:hover {\r\n        background-color: #d4c9a8;\r\n    }\r\n    \r\n    .el-menu-item.is-active {\r\n        background: linear-gradient(135deg, #a0866b 0%, #d4b999 100%) !important;\r\n        color: white !important;\r\n        box-shadow: 0 2px 8px rgba(139, 115, 85, 0.3) !important;\r\n        transform: translateY(-1px);\r\n    }\r\n    \r\n    .el-menu-item.is-active i {\r\n        color: white !important;\r\n    }\r\n    \r\n    .content-container {\r\n        display: flex;\r\n        flex-direction: column;\r\n        height: 100%;\r\n        padding: 25px;\r\n        background-color: rgba(255,255,255,0.7);\r\n        border-radius: 16px;\r\n        box-shadow: 0 4px 20px rgba(0,0,0,0.08);\r\n        border: 1px solid rgba(139, 115, 85, 0.1);\r\n    }\r\n    \r\n    .markdown-container {\r\n        padding: 15px 20px;\r\n        line-height: 1.6;\r\n        font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\r\n        color: #5a4a3a;\r\n    }\r\n    \r\n    .markdown-container h1, \r\n    .markdown-container h2, \r\n    .markdown-container h3 {\r\n        margin-top: 1.5em;\r\n        margin-bottom: 0.5em;\r\n        font-weight: 600;\r\n        color: #5a4a3a;\r\n    }\r\n    \r\n    .markdown-container p {\r\n        margin-bottom: 1em;\r\n    }\r\n    \r\n    /* 强制覆盖所有代码块样式 */\r\n    .markdown-container pre {\r\n        background: #e0e0e0 !important;\r\n        padding: 12px 16px !important;\r\n        border-radius: 6px !important;\r\n        overflow: auto !important;\r\n        margin: 0.5em 0 1.5em 0 !important;\r\n        border: 2px solid #c0c0c0 !important;\r\n        box-shadow: 0 3px 6px rgba(0,0,0,0.15) !important;\r\n    }\r\n    \r\n    /* 覆盖highlight.js生成的元素 */\r\n    .markdown-container pre code.hljs {\r\n        background: transparent !important;\r\n        padding: 0 !important;\r\n        color: inherit !important;\r\n    }\r\n    \r\n    /* 内联代码样式 */\r\n    .markdown-container code:not(pre code) {\r\n        font-family: SFMono-Regular, Consolas, \"Liberation Mono\", Menlo, monospace;\r\n        background: #f6f8fa;\r\n        padding: 2px 4px;\r\n        border-radius: 4px;\r\n        color: #8b7355;\r\n    }\r\n\r\n    .markdown-container blockquote {\r\n        border-left: 4px solid #8b7355;\r\n        background: #f8f6f2;\r\n        color: #6a5a3a;\r\n        margin: 1em 0;\r\n        padding: 0.7em 1.2em;\r\n        border-radius: 4px;\r\n        font-style: italic;\r\n    }\r\n\r\n    .typing-cursor {\r\n        display: inline-block;\r\n        width: 2px;\r\n        height: 1.2em;\r\n        margin-left: 2px;\r\n        background-color: #8b7355;\r\n        animation: blink 1s step-end infinite;\r\n        vertical-align: middle;\r\n    }\r\n    @keyframes blink {\r\n        0%, 100% { opacity: 1; }\r\n        50% { opacity: 0; }\r\n    }\r\n\r\n    .ai-loading {\r\n  display: inline-block;\r\n  min-width: 36px;\r\n  height: 22px;\r\n  vertical-align: middle;\r\n}\r\n.ai-loading .dot {\r\n  display: inline-block;\r\n  width: 8px;\r\n  height: 8px;\r\n  margin: 0 2px;\r\n  background: #8b7355;\r\n  border-radius: 50%;\r\n  animation: ai-bounce 1.2s infinite both;\r\n}\r\n.ai-loading .dot:nth-child(2) {\r\n  animation-delay: 0.2s;\r\n}\r\n.ai-loading .dot:nth-child(3) {\r\n  animation-delay: 0.4s;\r\n}\r\n@keyframes ai-bounce {\r\n  0%, 80%, 100% { transform: scale(0.7); opacity: 0.5; }\r\n  40% { transform: scale(1.2); opacity: 1; }\r\n}\r\n</style>\r\n\r\n<script>\r\nimport axios from 'axios';\r\nimport { Marked } from 'marked'\r\nimport { markedHighlight } from \"marked-highlight\";\r\nimport hljs from 'highlight.js/lib/core';\r\n\r\n\r\n\r\nexport default {\r\n    data() {\r\n        return {\r\n            summarizeMsg:\">tips:\\n > - 选择你要总结的文档\\n> - 生成总结\",\r\n            url:this.HOST+'/summarize/',\r\n            md:'# hhh',\r\n            // 动画相关\r\n            displayLines: [],\r\n            currentLine: \"\",\r\n            typingTimer: null,\r\n            lineIdx: 0,\r\n            typingLines: [],\r\n            loading: false, // 新增加载动画状态\r\n            uploadedFiles: [{ name: \"ccf2012-b.pdf\" }], // 新增：已上传文件列表\r\n            // currentFiles:[\"Big_Data_Quality_A_Survey.pdf\"],\r\n            selectedFileRow: null, // 新增：当前选中的文件行对象\r\n            selectedFileName: '', // 新增：当前选中文件名\r\n            username: '',\r\n            userInitial: '',\r\n        }\r\n    },\r\n    created() {\r\n        // 检查localStorage中是否有用户信息\r\n        const userInfo = localStorage.getItem('userInfo');\r\n        // 将JSON字符串转换为对象\r\n        const parsedUserInfo = JSON.parse(userInfo);\r\n        // 触发Vuex action来更新store中的用户信息\r\n        this.$store.dispatch('restoreUserInfo', parsedUserInfo);\r\n\r\n        // 获取当前用户信息\r\n        const user = this.$store.getters.getUserInfo;\r\n        if (user && user.username) {\r\n            this.username = user.username;\r\n            this.userInitial = user.username.charAt(0).toUpperCase();\r\n        } else {\r\n            this.username = '未登录';\r\n            this.userInitial = '?';\r\n        }\r\n    },\r\n    mounted() {\r\n        this.startLineAnimation(this.summarizeMsg)\r\n    },\r\n    computed: {\r\n        animatedHtml() {\r\n            let arr = this.displayLines.slice();\r\n            if (this.currentLine) {\r\n                arr.push(this.currentLine + '<span class=\"typing-cursor\">|</span>');\r\n            }\r\n            return this.markdownToHtml(arr.join('\\n'));\r\n        }\r\n    },\r\n    methods: {\r\n        markdownToHtml(message) {\r\n            if (!message) return '';\r\n            const marked = new Marked(\r\n                markedHighlight({\r\n                    pedantic: false,\r\n                    gfm: true,\r\n                    breaks: true,\r\n                    smartLists: true,\r\n                    xhtml: true,\r\n                    async: false,\r\n                    langPrefix: 'hljs language-',\r\n                    emptyLangClass: 'no-lang',\r\n                    highlight: (code) => {\r\n                        return hljs.highlightAuto(code).value\r\n                    }\r\n                })\r\n            );\r\n            return marked.parse(message);\r\n        },\r\n\r\n        gotoChat() {\r\n            this.$router.push({ path: '/chat' });\r\n        },\r\n        gotoTest() {\r\n            this.$router.push({ path: '/test' });\r\n        },\r\n        output(){\r\n            window.alert(this.summarizeMsg);\r\n        },\r\n        gotoPrj(){\r\n            this.$router.push({ path: '/project' });\r\n        },\r\n\r\n        // 上传文件前的校验\r\n        beforeUpload(file) {\r\n        const isLt10M = file.size / 1024 / 1024 < 10;\r\n        if (!isLt10M) {\r\n            this.$message.error('上传文件大小不能超过10MB!');\r\n        }\r\n        return isLt10M;\r\n        },\r\n\r\n        // 文件上传成功处理\r\n        handleUploadSuccess(response, file) {\r\n            console.log(response.data)\r\n            this.$message.success(`${file.name} 上传成功`);\r\n            // 新增：添加到已上传文件列表\r\n            this.uploadedFiles.push({ name: file.name });\r\n        },\r\n\r\n        // 处理文件行选择\r\n        handleFileRowSelect(row) {\r\n            this.selectedFileRow = row;\r\n            this.selectedFileName = row ? row.name : '';\r\n        },\r\n\r\n        startLineAnimation(msg) {\r\n            if (this.typingTimer) {\r\n                clearTimeout(this.typingTimer);\r\n                this.typingTimer = null;\r\n            }\r\n            this.displayLines = [];\r\n            this.currentLine = '';\r\n            this.lineIdx = 0;\r\n            this.typingLines = msg.split(/\\r?\\n/);\r\n            this.typeNextChar();\r\n        },\r\n        typeNextChar() {\r\n            if (this.lineIdx >= this.typingLines.length) {\r\n                this.currentLine = '';\r\n                this.displayLines = this.typingLines.slice();\r\n                return;\r\n            }\r\n            const line = this.typingLines[this.lineIdx];\r\n            if (this.currentLine.length < line.length) {\r\n                this.currentLine += line[this.currentLine.length];\r\n                this.typingTimer = setTimeout(this.typeNextChar, 10);\r\n            } else {\r\n                this.displayLines.push(this.currentLine);\r\n                this.currentLine = '';\r\n                this.lineIdx++;\r\n                this.typingTimer = setTimeout(this.typeNextChar, 120);\r\n            }\r\n        },\r\n        generateSummary(){\r\n            //window.alert(JSON.stringify(this.selectedFileName))\r\n            this.loading = true;\r\n            setTimeout(() => {\r\n                axios.get(this.url, {\r\n                    params: {\r\n                        fileName: this.selectedFileName\r\n                    }\r\n                }).then((response) => {\r\n                    this.summarizeMsg = response.data.AIMessage;\r\n                    this.loading = false;\r\n                    this.startLineAnimation(this.summarizeMsg);\r\n                })\r\n                .catch(error => {\r\n                    this.loading = false;\r\n                    this.$message.error('获取AI回复失败:' + error.message);\r\n                });\r\n            }, 15000);\r\n        },\r\n    }\r\n};\r\n</script>"], "mappings": ";AAwQA,OAAAA,KAAA;AACA,SAAAC,MAAA;AACA,SAAAC,eAAA;AACA,OAAAC,IAAA;AAIA;EACAC,KAAA;IACA;MACAC,YAAA;MACAC,GAAA,OAAAC,IAAA;MACAC,EAAA;MACA;MACAC,YAAA;MACAC,WAAA;MACAC,WAAA;MACAC,OAAA;MACAC,WAAA;MACAC,OAAA;MAAA;MACAC,aAAA;QAAAC,IAAA;MAAA;MAAA;MACA;MACAC,eAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,QAAA;MACAC,WAAA;IACA;EACA;EACAC,QAAA;IACA;IACA,MAAAC,QAAA,GAAAC,YAAA,CAAAC,OAAA;IACA;IACA,MAAAC,cAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAL,QAAA;IACA;IACA,KAAAM,MAAA,CAAAC,QAAA,oBAAAJ,cAAA;;IAEA;IACA,MAAAK,IAAA,QAAAF,MAAA,CAAAG,OAAA,CAAAC,WAAA;IACA,IAAAF,IAAA,IAAAA,IAAA,CAAAX,QAAA;MACA,KAAAA,QAAA,GAAAW,IAAA,CAAAX,QAAA;MACA,KAAAC,WAAA,GAAAU,IAAA,CAAAX,QAAA,CAAAc,MAAA,IAAAC,WAAA;IACA;MACA,KAAAf,QAAA;MACA,KAAAC,WAAA;IACA;EACA;EACAe,QAAA;IACA,KAAAC,kBAAA,MAAA/B,YAAA;EACA;EACAgC,QAAA;IACAC,aAAA;MACA,IAAAC,GAAA,QAAA9B,YAAA,CAAA+B,KAAA;MACA,SAAA9B,WAAA;QACA6B,GAAA,CAAAE,IAAA,MAAA/B,WAAA;MACA;MACA,YAAAgC,cAAA,CAAAH,GAAA,CAAAI,IAAA;IACA;EACA;EACAC,OAAA;IACAF,eAAAG,OAAA;MACA,KAAAA,OAAA;MACA,MAAAC,MAAA,OAAA7C,MAAA,CACAC,eAAA;QACA6C,QAAA;QACAC,GAAA;QACAC,MAAA;QACAC,UAAA;QACAC,KAAA;QACAC,KAAA;QACAC,UAAA;QACAC,cAAA;QACAC,SAAA,EAAAC,IAAA;UACA,OAAArD,IAAA,CAAAsD,aAAA,CAAAD,IAAA,EAAAE,KAAA;QACA;MACA,EACA;MACA,OAAAZ,MAAA,CAAAnB,KAAA,CAAAkB,OAAA;IACA;IAEAc,SAAA;MACA,KAAAC,OAAA,CAAAnB,IAAA;QAAAoB,IAAA;MAAA;IACA;IACAC,SAAA;MACA,KAAAF,OAAA,CAAAnB,IAAA;QAAAoB,IAAA;MAAA;IACA;IACAE,OAAA;MACAC,MAAA,CAAAC,KAAA,MAAA5D,YAAA;IACA;IACA6D,QAAA;MACA,KAAAN,OAAA,CAAAnB,IAAA;QAAAoB,IAAA;MAAA;IACA;IAEA;IACAM,aAAAC,IAAA;MACA,MAAAC,OAAA,GAAAD,IAAA,CAAAE,IAAA;MACA,KAAAD,OAAA;QACA,KAAAE,QAAA,CAAAC,KAAA;MACA;MACA,OAAAH,OAAA;IACA;IAEA;IACAI,oBAAAC,QAAA,EAAAN,IAAA;MACAO,OAAA,CAAAC,GAAA,CAAAF,QAAA,CAAAtE,IAAA;MACA,KAAAmE,QAAA,CAAAM,OAAA,IAAAT,IAAA,CAAApD,IAAA;MACA;MACA,KAAAD,aAAA,CAAA0B,IAAA;QAAAzB,IAAA,EAAAoD,IAAA,CAAApD;MAAA;IACA;IAEA;IACA8D,oBAAAC,GAAA;MACA,KAAA9D,eAAA,GAAA8D,GAAA;MACA,KAAA7D,gBAAA,GAAA6D,GAAA,GAAAA,GAAA,CAAA/D,IAAA;IACA;IAEAoB,mBAAA4C,GAAA;MACA,SAAArE,WAAA;QACAsE,YAAA,MAAAtE,WAAA;QACA,KAAAA,WAAA;MACA;MACA,KAAAF,YAAA;MACA,KAAAC,WAAA;MACA,KAAAE,OAAA;MACA,KAAAC,WAAA,GAAAmE,GAAA,CAAAE,KAAA;MACA,KAAAC,YAAA;IACA;IACAA,aAAA;MACA,SAAAvE,OAAA,SAAAC,WAAA,CAAAuE,MAAA;QACA,KAAA1E,WAAA;QACA,KAAAD,YAAA,QAAAI,WAAA,CAAA2B,KAAA;QACA;MACA;MACA,MAAA6C,IAAA,QAAAxE,WAAA,MAAAD,OAAA;MACA,SAAAF,WAAA,CAAA0E,MAAA,GAAAC,IAAA,CAAAD,MAAA;QACA,KAAA1E,WAAA,IAAA2E,IAAA,MAAA3E,WAAA,CAAA0E,MAAA;QACA,KAAAzE,WAAA,GAAA2E,UAAA,MAAAH,YAAA;MACA;QACA,KAAA1E,YAAA,CAAAgC,IAAA,MAAA/B,WAAA;QACA,KAAAA,WAAA;QACA,KAAAE,OAAA;QACA,KAAAD,WAAA,GAAA2E,UAAA,MAAAH,YAAA;MACA;IACA;IACAI,gBAAA;MACA;MACA,KAAAzE,OAAA;MACAwE,UAAA;QACAtF,KAAA,CAAAwF,GAAA,MAAAlF,GAAA;UACAmF,MAAA;YACAC,QAAA,OAAAxE;UACA;QACA,GAAAyE,IAAA,CAAAjB,QAAA;UACA,KAAArE,YAAA,GAAAqE,QAAA,CAAAtE,IAAA,CAAAwF,SAAA;UACA,KAAA9E,OAAA;UACA,KAAAsB,kBAAA,MAAA/B,YAAA;QACA,GACAwF,KAAA,CAAArB,KAAA;UACA,KAAA1D,OAAA;UACA,KAAAyD,QAAA,CAAAC,KAAA,eAAAA,KAAA,CAAA3B,OAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}