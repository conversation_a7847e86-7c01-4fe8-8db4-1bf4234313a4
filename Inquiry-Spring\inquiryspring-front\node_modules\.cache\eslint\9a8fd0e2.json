[{"C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\main.js": "1", "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\App.vue": "2", "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\store\\index.js": "3", "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\router\\index.js": "4", "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\views\\AboutView.vue": "5", "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\views\\guidePage\\loginPage.vue": "6", "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\views\\mainPage\\demoPage.vue": "7", "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\views\\guidePage\\createProject.vue": "8", "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\views\\mainPage\\summarizePage.vue": "9", "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\views\\mainPage\\homePage.vue": "10", "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\views\\mainPage\\chatPage.vue": "11", "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\views\\mainPage\\testPage.vue": "12", "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\views\\guidePage\\taskManage.vue": "13"}, {"size": 495, "mtime": 1749871984645, "results": "14", "hashOfConfig": "15"}, {"size": 104, "mtime": 1749871984645, "results": "16", "hashOfConfig": "15"}, {"size": 2285, "mtime": 1749892150806, "results": "17", "hashOfConfig": "15"}, {"size": 1431, "mtime": 1749892150806, "results": "18", "hashOfConfig": "15"}, {"size": 94, "mtime": 1749871984645, "results": "19", "hashOfConfig": "15"}, {"size": 10439, "mtime": 1749892150810, "results": "20", "hashOfConfig": "15"}, {"size": 441, "mtime": 1749871984645, "results": "21", "hashOfConfig": "15"}, {"size": 17720, "mtime": 1749893567458, "results": "22", "hashOfConfig": "15"}, {"size": 18923, "mtime": 1749893567461, "results": "23", "hashOfConfig": "15"}, {"size": 2065, "mtime": 1749871984645, "results": "24", "hashOfConfig": "15"}, {"size": 23850, "mtime": 1749893567461, "results": "25", "hashOfConfig": "15"}, {"size": 22475, "mtime": 1749892150810, "results": "26", "hashOfConfig": "15"}, {"size": 77, "mtime": 1749892150810, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "30"}, "9btwac", {"filePath": "31", "messages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "33"}, {"filePath": "34", "messages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "33"}, {"filePath": "40", "messages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "33"}, {"filePath": "44", "messages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "33"}, {"filePath": "50", "messages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\main.js", [], [], "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\App.vue", [], [], "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\store\\index.js", [], "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\router\\index.js", [], "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\views\\AboutView.vue", [], "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\views\\guidePage\\loginPage.vue", [], "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\views\\mainPage\\demoPage.vue", [], "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\views\\guidePage\\createProject.vue", [], "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\views\\mainPage\\summarizePage.vue", [], "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\views\\mainPage\\homePage.vue", [], "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\views\\mainPage\\chatPage.vue", [], "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\views\\mainPage\\testPage.vue", [], "C:\\Users\\<USER>\\Desktop\\问泉\\try1\\Inquiry-Spring\\inquiryspring-front\\src\\views\\guidePage\\taskManage.vue", []]