[project]
name = "InquirySpring"
version = "0.1.0"
description = "Intelligent Inquiry System"
authors = [
    { name="Ao-chii", email="<EMAIL>" }
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "Django==5.2",
    "django-cors-headers==4.7.0",
    "djangorestframework==3.16.0",
    "Pillow==11.1.0",
    "LangChain==0.3.25",
    "LangChain-Community==0.3.24",
    "LangChain-HuggingFace==0.2.0",
    "LangChain-text-splitters==0.3.8",
    "huggingface-hub==0.32.2",
    "chromadb==1.0.12",
    "sentence-transformers>=2.2.0",
    "PyPDF2>=3.0.0",
    "python-docx>=0.8.11",
    "python-multipart>=0.0.6",
    "google-generativeai==0.8.5",
    "torch==2.7.0",
    "transformers==4.52.3",
]

[project.urls]
"Home" = "https://github.com/Ao-chii/Inquiry-Spring"
